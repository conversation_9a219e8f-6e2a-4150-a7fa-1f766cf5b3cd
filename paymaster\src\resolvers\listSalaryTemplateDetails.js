// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { ApolloError } = require('apollo-server-lambda');
const commonFunctions = require('../common/salaryTemplateCommonFunctions');
const { Organizations } = require('aws-sdk');

/**
 * Main resolver function to get salary template details
 * @param {Object} parent - Parent object
 * @param {Object} args - Arguments passed to the resolver
 * @param {Object} context - Context object containing user info
 * @param {Object} info - GraphQL info object
 * @returns {Object} - Response with salary template details
 */
const listSalaryTemplateDetails = async (parent, args, context, info) => {
  let organizationDbConnection;

  try {
    console.log('Inside listSalaryTemplateDetails function');
    let responseData = [];
    const { logInEmpId: loginEmployeeId } = context;
    const userFormId = args.formId || 206;
    const isViewMode = args.isViewMode || false;

    organizationDbConnection = knex(context.connection.OrganizationDb);

    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      userFormId
    );
    if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
      throw '_DB0100';
    }
    const currencySymbolDetails = await organizationDbConnection(ehrTables.payrollGeneralSettings)
      .select('Payroll_Currency')
      .first();

    const currencySymbol = currencySymbolDetails?.Payroll_Currency || '₹';

    const tableConfig = getTableConfig(userFormId,args);
    const mainTableData = await getMainTableData(organizationDbConnection, tableConfig, args);

    // Get all record IDs upfront
    const recordIds = mainTableData.map(data => data[tableConfig.primaryKey]);

    // Get all allowance components in one query
    const allowanceComponentsByRecord = await getAllowanceComponentsByRecord(organizationDbConnection, recordIds, tableConfig);

    // Get all retirals components in one query
    const retiralsComponentsByRecord = await getRetiralsComponentsByRecord(organizationDbConnection, recordIds, tableConfig);

    // Get revision payslip components for salary revision records only
    let revisionPayslipComponentsByRecord = {};
    let retiralsPayslipComponentsByRecord = {};
    
    if (tableConfig.mainTable === ehrTables.salaryRevisionDetails && args.isViewMode === true) {
      revisionPayslipComponentsByRecord = await getRevisionPayslipComponentsByRecord(organizationDbConnection, recordIds, tableConfig) || {};
      retiralsPayslipComponentsByRecord = await getRetiralsPayslipByRecord(organizationDbConnection, recordIds, tableConfig) || {};
    }
    
    // Build response data
    responseData = mainTableData.map(data => {
      const recordId = data[tableConfig.primaryKey];
      let recordData = {
        ...formatMainTableData(data, tableConfig),
      };

      if (isViewMode) {
        recordData.allowances = {
          allowanceArray: allowanceComponentsByRecord[recordId]?.Allowance || [],
          fixedAllowanceArray: allowanceComponentsByRecord[recordId]?.Fixed_Allowance || [],
          bonusArray: allowanceComponentsByRecord[recordId]?.Bonus || [],
          flexiBenefitPlanArray: allowanceComponentsByRecord[recordId]?.Flexible_Benefit_Plan || [],
          reimbursementArray: allowanceComponentsByRecord[recordId]?.Reimbursement || [],
          basicPayArray: allowanceComponentsByRecord[recordId]?.Basic_Pay || []
        };
        recordData.retirals = retiralsComponentsByRecord[recordId] || [];

        if (tableConfig.mainTable === ehrTables.salaryRevisionDetails && args.isViewMode === true) {
          recordData.revisionPayslipComponents = revisionPayslipComponentsByRecord?.[recordId]?.revisionPayslipComponents || [];
          recordData.retiralsPayslipComponents = retiralsPayslipComponentsByRecord?.[recordId]?.retiralsPayslipComponents || [];
        }
      }

      return recordData;
    });

    return {
      errorCode: "",
      message: `${tableConfig.entityName} details retrieved successfully`,
      currencySymbol: currencySymbol,
      templateDetails: JSON.stringify(responseData)
    };
  } catch (error) {
    console.log('Error in listSalaryTemplateDetails() function main catch block', error);

    // Handle error response
    const errResult = commonLib.func.getError(error, 'PFF0018');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    // Clean up database connections
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
};

/**
 * Get table configuration based on form ID
 * @param {number} formId - Form ID to determine table configuration
 * @returns {Object} - Configuration object with table details
 */
function getTableConfig(formId,args) {
  switch (parseInt(formId)) {
    case 206:
      return {
        entityName: 'Salary template',
        mainTable: ehrTables.salaryTemplate,
        allowanceTable: ehrTables.templateAllowanceComponents,
        retiralsTable: ehrTables.templateRetiralComponents,
        primaryKey: 'Template_Id',
        foreignKey: 'Template_Id',
        statusField: 'Template_Status',
        defaultOrderBy: 'Template_Status'
      };
    case 207:
      return {
        entityName: 'Salary details',
        mainTable: args.includeHistoricalRecords ? 'employee_salary_history' : ehrTables.employeeSalaryDetails,
        allowanceTable: args.includeHistoricalRecords ? 'employee_allowance_history' : ehrTables.employeeSalaryAllowance,
        retiralsTable: args.includeHistoricalRecords ? 'employee_retirals_history' : ehrTables.employeeSalaryRetirals,
        primaryKey: args.includeHistoricalRecords ? 'Salary_History_Id' : 'Employee_Id',
        foreignKey: args.includeHistoricalRecords ? 'Salary_History_Id' : 'Employee_Id',
        statusField: null,
        defaultOrderBy: args.includeHistoricalRecords ? 'Salary_History_Id' : 'Employee_Id',
        isHistorical: args.includeHistoricalRecords || false
      };
    case 360: // Salary Revision
      return {
        entityName: 'Salary revision',
        mainTable: ehrTables.salaryRevisionDetails,
        allowanceTable: ehrTables.salaryRevisionAllowance,
        retiralsTable: ehrTables.salaryRevisionRetirals,
        primaryKey: 'Revision_Id',
        foreignKey: 'Revision_Id',
        statusField: null,
        defaultOrderBy: 'Employee_Id'
      };
    default:
      throw 'PFF0019';
  }
}

/**
 * Get main table data with filtering and ordering
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} tableConfig - Table configuration
 * @param {Object} args - Query arguments
 * @returns {Promise<Array>} - Array of records from main table
 */
async function getMainTableData(organizationDbConnection, tableConfig, args) {
  let query;
  const isViewMode = args.isViewMode || false;
  const isDropdown = args.isDropdown || false;

  // 1. Determine table alias upfront
  const isSalaryTable = [ehrTables.employeeSalaryDetails, ehrTables.salaryRevisionDetails, 'employee_salary_history'].includes(tableConfig.mainTable);
  const mainTableAlias = isSalaryTable ?
    (tableConfig.mainTable === ehrTables.employeeSalaryDetails ? 'ESD' :
     tableConfig.mainTable === 'employee_salary_history' ? 'ESH' : 'RSD') :
    tableConfig.mainTable;
  if (isSalaryTable) {
    query = organizationDbConnection(`${tableConfig.mainTable} as ${mainTableAlias}`)
      .select(
        `${mainTableAlias}.*`,
        'ST.Template_Name',
        'EJ.Emp_Status',
        organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as EmployeeName"),
        organizationDbConnection.raw("(CASE WHEN EJ.User_Defined_EmpId IS NOT NULL THEN EJ.User_Defined_EmpId ELSE EPI3.Employee_Id END) as userDefinedEmpId"),
        organizationDbConnection.raw(`(
          SELECT ESL.Amount
          FROM ?? as ESL
          INNER JOIN ?? as A ON ESL.Allowance_Id = A.Allowance_Id
          INNER JOIN ?? as AT ON A.Allowance_Type_Id = AT.Allowance_Type_Id
          WHERE ESL.?? = ??.??
          AND AT.Is_Basic_Pay = ?
          limit 1
        ) as Basic_Pay`, [
          tableConfig.allowanceTable,
          ehrTables.allowances,
          ehrTables.allowanceType,
          tableConfig.foreignKey,
          mainTableAlias,
          tableConfig.primaryKey,
          'Yes'
        ])
      )

      .leftJoin(`${ehrTables.salaryTemplate} as ST`, `${mainTableAlias}.Template_Id`, 'ST.Template_Id')
      .leftJoin(`${ehrTables.empPersonalInfo} as EPI3`, `${mainTableAlias}.Employee_Id`, 'EPI3.Employee_Id')
      .leftJoin(`${ehrTables.empJob} as EJ`, 'EJ.Employee_Id', 'EPI3.Employee_Id')
    if (tableConfig.mainTable === ehrTables.employeeSalaryDetails && isViewMode) {
      query.select('ESC.*')
        .leftJoin(`${ehrTables.employeeSalaryConfiguration} as ESC`, `${mainTableAlias}.Employee_Id`, 'ESC.Employee_Id');
    }
    // For historical records, don't add salary configuration
    if (tableConfig.mainTable === 'employee_salary_history' && isViewMode) {
      // Historical records don't need salary configuration join
    }
  } else {
    query = organizationDbConnection(tableConfig.mainTable).select('*');
  }


  // 3. Common joins for AddedBy/UpdatedBy (applies to ALL forms)
  query
    .leftJoin(`${ehrTables.empPersonalInfo} as EPI`, 'EPI.Employee_Id', `${mainTableAlias}.Added_By`)
    .leftJoin(`${ehrTables.empPersonalInfo} as EPI2`, 'EPI2.Employee_Id', `${mainTableAlias}.Updated_By`)
    .select([
      organizationDbConnection.raw("CONCAT_WS(' ', EPI.Emp_First_Name, EPI.Emp_Middle_Name, EPI.Emp_Last_Name) as AddedByName"),
      organizationDbConnection.raw("CONCAT_WS(' ', EPI2.Emp_First_Name, EPI2.Emp_Middle_Name, EPI2.Emp_Last_Name) as UpdatedByName"),
    ]);
  // 4. Conditional filtering
  if (args.templateId) {
    query.where(`${mainTableAlias}.${tableConfig.primaryKey}`, args.templateId);
  }
  if(args.id && tableConfig.mainTable === ehrTables.salaryRevisionDetails){
    query.where(`${mainTableAlias}.${tableConfig.primaryKey}`, args.id);
  }
  if (args.employeeId) {
    query.where(`${mainTableAlias}.Employee_Id`, args.employeeId);
  } else {
    if (tableConfig.mainTable === 'employee_salary_history') {
      // No additional filtering for historical records - show all history
    }

    if (tableConfig.mainTable === ehrTables.salaryTemplate && isDropdown) {
      query.where(`${mainTableAlias}.Template_Status`, 'Active');
    } else if (tableConfig.statusField) {
      query.where(`${mainTableAlias}.${tableConfig.statusField}`, 'Active');
    }
  }

  // 5. Ordering
  if (tableConfig.defaultOrderBy) {
    query.orderBy(`${mainTableAlias}.${tableConfig.defaultOrderBy}`);
  }

  const result = await query;
  return result.length > 0 ? result : [];
}

/**
 * Format main table data based on table type
 * @param {Object} data - Raw data from database
 * @param {Object} tableConfig - Table configuration
 * @returns {Object} - Formatted data object
 */
function formatMainTableData(data, tableConfig) {
  switch (tableConfig.mainTable) {
    case ehrTables.salaryTemplate:
      return {
        'Template_Id': data.Template_Id,
        'Template_Name': data.Template_Name,
        'Annual_CTC': data.Annual_Ctc,
        'Template_Status': data.Template_Status,
        'Description': data.Description,
        'Added_On': data.Added_On,
        'Updated_On': data.Updated_On,
        'AddedByName': data.AddedByName,
        'UpdatedByName': data.UpdatedByName
      };
    case ehrTables.employeeSalaryDetails:
      // For salary form, include all fields from the query
      const formattedData = {
        'Employee_Salary_Id': data.Employee_Salary_Id,
        'Employee_Id': data.Employee_Id,
        'Template_Id': data.Template_Id,
        'Template_Name': data.Template_Name,
        'Basic_Pay': data.Basic_Pay,
        'Effective_From': data.Effective_From,
        'Annual_CTC': data.Annual_Ctc,
        'Annual_Gross_Salary': data.Annual_Gross_Salary,
        'Monthly_Gross_Salary': data.Monthly_Gross_Salary,
        'Salary_Effective_Month': data.Salary_Effective_Month,
        'Employee_Name': data.EmployeeName,
        'Emp_Status' : data.Emp_Status,
        'User_Defined_EmpId': data.userDefinedEmpId,
        'Added_On': data.Added_On,
        'Updated_On': data.Updated_On,
        'AddedByName': data.AddedByName,
        'UpdatedByName': data.UpdatedByName
      };

      // Add configuration fields if they exist (when isViewMode is true)
      if (data.Eligible_For_Overtime !== undefined) {
        Object.assign(formattedData, {
          'Eligible_For_Overtime': data.Eligible_For_Overtime,
          'Overtime_Allocation': data.Overtime_Allocation,
          'Overtime_Wage_Index': data.Overtime_Wage_Index,
          'Overtime_Slab': data.Overtime_Slab,
          'Overtime_Wage': data.Overtime_Wage,
          'Eligible_For_Pf': data.Eligible_For_Pf,
          'Eligible_For_Pension': data.Eligible_For_Pension,
          'Exempt_EDLI': data.Exempt_EDLI,
          'UAN': data.UAN,
          'Pf_PolicyNo': data.Pf_PolicyNo,
          'Eligible_For_ESI': data.Eligible_For_ESI,
          'ESI_Number': data.ESI_Number,
          'ESI_Contribution_End_Date': data.ESI_Contribution_End_Date,
          'Reason_Id': data.Reason_Id,
          'Eligible_For_Vpf': data.Eligible_For_Vpf,
          'Vpf_Type': data.Vpf_Type,
          'Vpf_Employee_Share': data.Vpf_Employee_Share,
          'Vpf_Employee_Share_Amount': data.Vpf_Employee_Share_Amount,
          'Eligible_For_Insurance': data.Eligible_For_Insurance,
          'Eligible_For_Nps': data.Eligible_For_Nps,
          'Nps_Number': data.Nps_Number,
          'Eligible_For_Gratuity': data.Eligible_For_Gratuity,
          'Eligible_For_PT': data.Eligible_For_PT,
          'Bond_Recovery_Applicable': data.Bond_Recovery_Applicable,
          'Minimum_Months_To_Be_Served': data.Minimum_Months_To_Be_Served,
          'Bond_Value': data.Bond_Value,
          'Eligible_For_Contractor_Tds': data.Eligible_For_Contractor_Tds,
          'Tax_Section_Id': data.Tax_Section_Id,
          'Eligible_For_Teacher_Provident_Fund': data.Eligible_For_Teacher_Provident_Fund,
          'TPF_Type': data.TPF_Type,
          'TPF_Employee_Share_Amount': data.TPF_Employee_Share_Amount,
          'TPF_Employee_Share': data.TPF_Employee_Share,
          'TPF_Number': data.TPF_Number,
          'Eligible_For_Contribution_Pension_Scheme': data.Eligible_For_Contribution_Pension_Scheme,
          'CPS_Type': data.CPS_Type,
          'CPS_Employee_Share_Amount': data.CPS_Employee_Share_Amount,
          'CPS_Employer_Share_Amount': data.CPS_Employer_Share_Amount,
          'CPS_Employee_Share': data.CPS_Employee_Share,
          'CPS_Employer_Share': data.CPS_Employer_Share,
          'CPS_Number': data.CPS_Number,
          'Eligible_For_Special_Provident_Fund': data.Eligible_For_Special_Provident_Fund,
          'SPF_Employee_Share': data.SPF_Employee_Share,
          'SPF_End_Month': data.SPF_End_Month,
          'SPF_Number': data.SPF_Number,
          'Salary_Calculation_Scheme': data.Salary_Calculation_Scheme,
          'PTKP_Id': data.PTKP_Id,
          'Tax_Object_Id': data.Tax_Object_Id,
          'Daily_Employee': data.Daily_Employee,
          'Get_Facilities': data.Get_Facilities,
          'SKB_Number': data.SKB_Number,
          'DTP_Number': data.DTP_Number

        });
      }

      return formattedData;
    case 'employee_salary_history':
      // For salary history, format similar to salary details but with history-specific fields
      return {
        'Salary_History_Id': data.Salary_History_Id,
        'Employee_Id': data.Employee_Id,
        'Template_Id': data.Template_Id,
        'Template_Name': data.Template_Name,
        'Basic_Pay': data.Basic_Pay,
        'Effective_From': data.Effective_From,
        'Effective_To': data.Effective_To,
        'Annual_CTC': data.Annual_Ctc,
        'Annual_Gross_Salary': data.Annual_Gross_Salary,
        'Monthly_Gross_Salary': data.Monthly_Gross_Salary,
        'Salary_Effective_Month': data.Salary_Effective_Month,
        'Employee_Name': data.EmployeeName,
        'Emp_Status': data.Emp_Status,
        'User_Defined_EmpId': data.userDefinedEmpId,
        'Added_On': data.Added_On,
        'Updated_On': data.Updated_On,
        'AddedByName': data.AddedByName,
        'UpdatedByName': data.UpdatedByName
      };
    case ehrTables.salaryRevisionDetails:
      return {
        'Revision_Id': data.Revision_Id,
        'Employee_Id': data.Employee_Id,
        'Template_Id': data.Template_Id,
        'Template_Name': data.Template_Name,
        'Basic_Pay': data.Basic_Pay,
        'Effective_From': data.Effective_From,
        'Payout_Month': data.Payout_Month,
        'Revise_Ctc_By_Percentage': data.Revise_Ctc_By_Percentage,
        'Annual_CTC': data.Annual_Ctc,
        'Employee_Name': data.EmployeeName,
        'Emp_Status': data.Emp_Status,
        'User_Defined_EmpId': data.userDefinedEmpId,
        'Annual_Gross_Salary': data.Annual_Gross_Salary,
        'Monthly_Gross_Salary': data.Monthly_Gross_Salary,
        'revisionType': data.Revision_Type,
        'revisionStatus': data.Revision_Status,
        'Salary_Effective_Month': data.Salary_Effective_Month,
        'previousCtc': data.Previous_Ctc,
        'Added_On': data.Added_On,
        'Updated_On': data.Updated_On,
        'AddedByName': data.AddedByName,
        'UpdatedByName': data.UpdatedByName
      };
    default:
      return data;
  }
}

/**
 * Get allowance components for multiple records in one query
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their allowance components
 */


async function getAllowanceComponentsByRecord(organizationDbConnection, recordIds, tableConfig) {
  try {
    const tableAlias = tableConfig.mainTable === ehrTables.salaryTemplate ? 'TAC' :
                      tableConfig.mainTable === 'employee_salary_history' ? 'EAH' : 'ESA';
    const allowanceTypeDetails = await organizationDbConnection
      .select(
        `${tableAlias}.*`,
        'A.Amount as Org_Amount',
        'AT.*',
        organizationDbConnection.raw('GROUP_CONCAT(BA.Form_Id) as Form_Id'),
        'A.FBP_Max_Declaration',
      )
      .from(`${tableConfig.allowanceTable} as ${tableAlias}`)
      .leftJoin(`${ehrTables.allowances} as A`, `A.Allowance_Id`, `${tableAlias}.Allowance_Id`)
      .leftJoin(`${ehrTables.allowanceType} as AT`, 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
      .leftJoin('allowance_type_benefit_association as BA',
        'AT.Allowance_Type_Id', 'BA.Allowance_Type_Id')
      .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BA.Form_Id')
      .groupBy(`${tableAlias}.${tableConfig.foreignKey}`, `${tableAlias}.Allowance_Id`)
      .whereIn(`${tableAlias}.${tableConfig.foreignKey}`, recordIds);

    // Group allowance components by record ID
    const componentsByRecord = {};
    for (let allowanceData of allowanceTypeDetails) {
      const recordId = allowanceData[tableConfig.foreignKey];
      if (!componentsByRecord[recordId]) {
        componentsByRecord[recordId] = {
          Allowance: [],
          Fixed_Allowance: [],
          Bonus: [],
          Flexible_Benefit_Plan: [],
          Reimbursement: [],
          Basic_Pay: []
        };
      }

      const response = await commonFunctions.benefitAssociation(allowanceData);

      const mappedData = mapAllowanceData(response);

      if (allowanceData['Formula_Based'].toLowerCase() === 'yes') {
        componentsByRecord[recordId].Fixed_Allowance.push(mappedData);
      } else if (
        allowanceData['Is_Claim_From_Reimbursement'].toLowerCase() === 'yes'
      ) {
        componentsByRecord[recordId].Reimbursement.push(mappedData);
      }
      else if (
        allowanceData['Is_Flexi_Benefit_Plan'].toLowerCase() === 'yes'
      ) {
        componentsByRecord[recordId].Flexible_Benefit_Plan.push(mappedData);
      }
      else if (allowanceData['Allowance_Mode'].toLowerCase() === 'bonus') {
        componentsByRecord[recordId].Bonus.push(mappedData);
      }
      else if (allowanceData['Allowance_Mode'].toLowerCase() === 'non bonus' && allowanceData['Is_Basic_Pay'].toLowerCase() === 'no') {
        componentsByRecord[recordId].Allowance.push(mappedData);
      }
      else if (allowanceData['Is_Basic_Pay'] && allowanceData['Is_Basic_Pay'].toLowerCase() === 'yes'){
        componentsByRecord[recordId].Basic_Pay.push(mappedData);
      }
    }

    return componentsByRecord;
  } catch (error) {
    console.log('Error in getAllowanceComponentsByRecord catch block', error);
    throw error;
  }
}

/**
 * Get revision payslip components for multiple records
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their revision payslip components
 */
async function getRevisionPayslipComponentsByRecord(organizationDbConnection, recordIds, tableConfig) {
  try {
    const payslipComponentsByRecord = {};

    const revisionPayslipDetails = await organizationDbConnection
      .select(
        'RPD.Revision_Id',
        'A.Allowance_Id',
        organizationDbConnection.raw("CONCAT_WS(' ', 'Arrear', AT.Allowance_Name) as Allowance_Name"),
        'RPD.Component_Id',
        'RPD.Component_Amount'
      )
      .from(`${ehrTables.revisionPayslipDetails} as RPD`)
      .leftJoin(`${ehrTables.allowances} as A`, 'A.Allowance_Id', 'RPD.Component_Id')
      .leftJoin(`${ehrTables.allowanceType} as AT`, 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
      .whereIn('RPD.Revision_Id', recordIds)
      .orderBy([
        { column: 'AT.Allowance_Sequence', order: 'asc' },
        { column: 'AT.Allowance_Name', order: 'asc' }
      ]);

    // Group components by record ID
    for (let allowanceData of revisionPayslipDetails) {
      const recordId = allowanceData.Revision_Id;
      if (!payslipComponentsByRecord[recordId]) {
        payslipComponentsByRecord[recordId] = {
          revisionPayslipComponents: []
        };
      }
      const mappedData = mapRevisionPayslipData(allowanceData);
      payslipComponentsByRecord[recordId].revisionPayslipComponents.push(mappedData);
    }

    return payslipComponentsByRecord;
  } catch (error) {
    console.log('Error in revisionPayslipComponentsByRecord catch block', error);
    throw error;
  }
}

/**
 * Get retirals payslip components for multiple records
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their retirals payslip components
 */

  async function getRetiralsPayslipByRecord(organizationDbConnection, recordIds, tableConfig) {
      try {
        const retiralsComponentsByRecord = {};
        const retiralsComponents = await organizationDbConnection
          .select(
            'PRD.*',
            'IC.Insurance_Name AS Insurance_Name',
            'IC.Insurance_Type AS Insurance_Type',
            'EF.Form_Name AS Retirals_Name'
          )
          .from('payslip_retirals_revision_details as PRD')
          .leftJoin(`${ehrTables.insuranceConfiguration} as IC`, 'IC.InsuranceType_Id', 'PRD.Retirals_Id')
          .leftJoin(`${ehrTables.ehrForms} as EF`, 'EF.Form_Id', 'PRD.Form_Id')
          .whereIn(`PRD.${tableConfig.foreignKey}`, recordIds)
          for(let retiralsData of retiralsComponents){
            const recordId = retiralsData[tableConfig.foreignKey];
            if (!retiralsComponentsByRecord[recordId]) {
              retiralsComponentsByRecord[recordId] = {
                retiralsPayslipComponents: []
              };
            }
            const mappedData = mapRetiralsData(retiralsData);
            if(!retiralsComponentsByRecord[recordId]){
              retiralsComponentsByRecord[recordId] = [];
            }
            retiralsComponentsByRecord[recordId].retiralsPayslipComponents.push(mappedData);
          }
          return retiralsComponentsByRecord;
      } catch (error) {
        console.log('Error in getRetiralsComponentsByRecord catch block', error);
        throw error;
      }
    }
/**
 * Map allowance data to consistent format
 * @param {Object} response - Raw allowance data
 * @returns {Object} - Formatted allowance data
 */
function mapAllowanceData(response) {
  return {
    'Allowance_Id': response['Allowance_Id'],
    'Allowance_Name': response['Allowance_Name'],
    'Allowance_Type': response['Allowance_Type'],
    'Is_Basic_Pay': response['Is_Basic_Pay'],
    'Period': response['Period'],
    'Amount': response['Amount'],
    'AllowanceWages': response['Allowance_Wages'],
    'Percentage': response['Percentage'],
    'orgAmount': response['Org_Amount'],
    'Form_Id': response['Form_Id'],
    'Form_Name': response['Form_Name'],
    'pfMapped': response['pfMapped'],
    'variableInsuranceMapped': response['variableInsuranceMapped'],
    'fixedInsuranceMapped': response['fixedInsuranceMapped'],
    'gratuityMapped': response['gratuityMapped'],
    'npsMapped': response['npsMapped']
  };
}

function mapRevisionPayslipData(response){
  return {
    'Allowance_Id': response['Allowance_Id'],
    'Allowance_Name': response['Allowance_Name'],
    'Amount': response['Component_Amount']
  };
}
function mapRetiralsData(response){
  return {
    'Form_Id': response['Form_Id'],
    'Retirals_Id': response['Retirals_Id'],
    'Employee_Share_Amount': response['Employee_Share_Amount'],
    'Employer_Share_Amount': response['Employer_Share_Amount'],
    'Insurance_Name': response['Insurance_Name'],
    'Insurance_Type': response['Insurance_Type'],
    'Retirals_Name': response['Retirals_Name'],
  };
}

/**
 * Get retirals components for multiple records in one query
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} recordIds - Array of record IDs
 * @param {Object} tableConfig - Table configuration
 * @returns {Promise<Object>} - Object mapping record IDs to their retirals components
 */
async function getRetiralsComponentsByRecord(organizationDbConnection, recordIds, tableConfig) {
  try {
    const retiralsComponents = await organizationDbConnection
      .select(
        'TRC.*',
        'IC.Insurance_Name AS Insurance_Name',
        'IC.Insurance_Type AS Insurance_Type',
        'EF.Form_Name as Retirals_Name',
        organizationDbConnection.raw('GROUP_CONCAT(A2.Allowance_Id) as Allowance_Ids')
      )
      .from(`${tableConfig.retiralsTable} as TRC`)
      .leftJoin('allowance_type_benefit_association as BA',
        'TRC.Form_Id', 'BA.Form_Id')
        .leftJoin(ehrTables.allowances + ' as A2', 'A2.Allowance_Type_Id', 'BA.Allowance_Type_Id')
      .innerJoin(`${ehrTables.ehrForms} as EF`, 'EF.Form_Id', 'TRC.Form_Id')
      .leftJoin(`${ehrTables.insuranceConfiguration} as IC`, 'IC.InsuranceType_Id', 'TRC.Retirals_Id')
      .whereIn(`TRC.${tableConfig.foreignKey}`, recordIds)
      .groupBy(`TRC.${tableConfig.foreignKey}`,`TRC.Form_Id`,`TRC.Retirals_Id`);

    // Group retirals by record ID
    const retiralsByRecord = {};
    for (let retiralsData of retiralsComponents) {
      const recordId = retiralsData[tableConfig.foreignKey];
      if (!retiralsByRecord[recordId]) {
        retiralsByRecord[recordId] = [];
      }
      retiralsByRecord[recordId].push(retiralsData);
    }

    return retiralsByRecord;
  } catch (error) {
    console.log('Error in getRetiralsComponentsByRecord catch block', error);
    throw error;
  }
}
// Export resolvers
const resolvers = {
  Query: {
    listSalaryTemplateDetails
  }
};

exports.resolvers = resolvers;