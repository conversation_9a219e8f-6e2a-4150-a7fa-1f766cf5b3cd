{"name": "paymaster", "version": "1.0.0", "description": "\"Project for <PERSON><PERSON>, <PERSON>ary Template and Payroll.\"", "main": "handler.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "sls offline --stage dev --region ap-south-1 --reload<PERSON>andler", "local": "sls offline --stage local --region ap-south-1 --reload<PERSON>andler"}, "repository": {"type": "git", "url": "git+https://gitlab.com/cksiva09/hrapp-payrollms.git"}, "keywords": ["Salary", "salary-Template", "payslip"], "author": "<PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://gitlab.com/cksiva09/hrapp-payrollms/issues"}, "homepage": "https://gitlab.com/cksiva09/hrapp-payrollms#README", "dependencies": {"@cksiva09/hrapp-corelib": "git+https://cksiva09:<EMAIL>/cksiva09/hrapp-corelib.git", "apollo-server-lambda": "^2.16.1", "aws-sdk": "^2.737.0", "format-utils": "0.0.3", "fs": "0.0.1-security", "graphql": "^14.6.0", "knex": "^0.20.12", "moment-timezone": "^0.5.31", "mysql": "^2.18.1", "number-to-words": "^1.2.4", "serverless-domain-manager": "^7.1.2", "serverless-prune-plugin": "^2.0.2"}, "devDependencies": {"serverless-offline": "^13.2.0"}}