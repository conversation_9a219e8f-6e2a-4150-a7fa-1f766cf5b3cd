const getEmployerRemittanceReport = require('../src/roresolvers/reports/getEmployerRemittanceReport');
const getMemberContributionReport = require('../src/roresolvers/reports/getMemberContributionReport');
const getR3MonthlyContributionReport = require('../src/roresolvers/reports/getR3MonthlyContributionReport');
const getR5ContributionReport = require('../src/roresolvers/reports/getR5ContributionReport');
const getAnnualInformationReturnReport = require('../src/roresolvers/reports/getAnnualInformationReturnReport');
const getCertificateOfPaymentReport = require('../src/roresolvers/reports/getCertificateOfPaymentReport');
const getIndonesiaStatutoryReport = require('./roresolvers/reports/getIndonesiaStatutoryReport');
const generateIndonesiaReports = require('./resolvers/generateIndonesiaReports');
const getPND1Report=require('./roresolvers/reports/getPND1Report');
const getWithHoldTaxCertificate=require('./roresolvers/reports/getwithHoldTaxCertificate');
const getPND53Report=require('./roresolvers/reports/getPND53Report');
const getPND3Report=require('./roresolvers/reports/getPND3Report');
const getPND1MonthlyReport = require('./roresolvers/reports/getPND1MonthlyReport');
const calculateSalary = require('./roresolvers/salary/calculateSalary');
const calculateSalaryArrears = require('./roresolvers/salary/calculateSalaryArrears');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        getEmployerRemittanceReport,
        getMemberContributionReport,
        getR3MonthlyContributionReport,
        getR5ContributionReport,
        getAnnualInformationReturnReport,
        getCertificateOfPaymentReport,
        getIndonesiaStatutoryReport,
        generateIndonesiaReports,
        getPND1Report,
        getWithHoldTaxCertificate,
        getPND53Report,
        getPND3Report,
        getPND1MonthlyReport,
        calculateSalary,
        calculateSalaryArrears
    )
}
exports.resolvers = resolvers;