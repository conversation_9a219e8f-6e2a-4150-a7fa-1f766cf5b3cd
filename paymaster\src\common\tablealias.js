/** Alias name for ehr tables */
module.exports.ehrTables = {
    location        : 'location',
    salaryTemplate  : 'salary_template',
    allowanceType   : 'allowance_type',
    allowances      : 'allowances',
    templateAllowanceComponents : 'template_allowance_components',
    templateRetiralComponents   : 'template_retiral_components',
    forms : 'forms',
    ehrForms : 'ehr_forms',
    customizationForms : 'customization_forms',
    variableInsurance : 'variable_insurance',
    fixedInsurance : 'fixed_insurance',
    insuranceType : 'insurance_type',
    fixedHealthInsurance : 'fixed_health_insurance',
    fixedHealthInsuranceType : 'fixed_health_insurance_type',
    unitData : 'unit_data',
    employeeSalaryDetails: 'employee_salary_details',
    orgEtf:'org_etf',
    insurancetypeGrade:'insurancetype_grade',
    gratuitySettings:'gratuity_settings',
    orgPf:'org_pf',
    employeeSalaryAllowance:'employee_salary_allowance',
    employeeSalaryRetirals:'employee_salary_retirals',
    designation: 'designation',
    empJob: 'emp_job',
    employeeSalaryConfiguration:'employee_salary_configuration',
    allowanceBenefitAssociation:'allowance_type_benefit_association',
    salaryPayslip:'salary_payslip',
    hourlywagesPayslip:'hourlywages_payslip',
    salaryDeduction : 'salary_deduction',
    hourlywageDeduction : 'hourlywage_deduction',
    overtimeDetails:'overtime_details',
    empPersonalInfo:'emp_personal_info',
    providentFund: 'provident_fund',
    orgDetails: 'org_details',
    taxConfiguration: 'tax_configuration',
    empPfPayment:'emp_pf_payment',
    empInsurancePayment: 'emp_insurance_payment',
    empEtfPayment: 'emp_etf_payment',
    empResignation: 'emp_resignation',
    empInsurance: 'emp_insurancepolicyno',
    city: 'city',
    state: 'state',
    country:'country',
    tdsPayment: 'tds_payment',
    tdsPaymentTracker: 'tds_payment_tracker',
    salaryDetails: 'salary_details',
    monthlyForm16Snapshot:'monthly_form16_snapshot',
    contactDetails:'contact_details',
    insuranceConfiguration:'insurance_configuration',
    payrollGeneralSettings:'payroll_general_settings',
    salaryRevisionDetails:'salary_revision_details',
    salaryRevisionAllowance:'salary_revision_allowance',
    salaryRevisionRetirals:'salary_revision_retirals',
    philHealthSlabs:'philhelath_slabs',
    benefitForms:'benefit_forms',
    providentFundSettings:'provident_fund_settings',
    payrollRoundOffSettings:'payroll_round_off_settings',
    socialSecurityScheme:'social_security_scheme_slabs',
    npsSlab:'nps_slabs',
    insuranceContributionConfiguration:'insurance_contribution_configuration',
    workflows:'workflows',
    workflowModule:'workflow_module',
    esiStatutoryConfiguration:'esi_statutory_configuration',
    payslipRetiralsRevisionDetails:'payslip_retirals_revision_details',
    revisionPayslipDetails:'revision_payslip_details',
    payslipRetiralsRevisionDetails:'payslip_retirals_revision_details',
    taUserTask: 'ta_user_task',
    taProcessInstance: 'ta_process_instance',
    taProcessInstanceHistory: 'ta_process_instance_history',
    taWorkFlow: 'ta_workflow',
    taUserTaskHistory: 'ta_user_task_history'
}; 