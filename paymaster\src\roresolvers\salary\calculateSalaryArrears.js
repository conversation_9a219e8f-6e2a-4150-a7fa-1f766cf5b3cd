const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const moment = require('moment-timezone');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../common/tablealias');
const { getRoundOffValue, getRoundOffSettings } = require('../../common/commonfunctions');
const { formId } = require('../../common/appconstants');

let roundOffSettings;
module.exports.calculateSalaryArrears = async (parent, args, context, info) => {
    let organizationDbConnection;

    try {
        organizationDbConnection = context.orgdb ? context.orgdb : knex(context.connection.OrganizationDb);

        // Validate required arguments
        if (!args.employeeId || !args.revisionId) {
            throw new Error('Employee ID and Revision ID are required');
        }

        // Step 1: Get salary details and arrears details in parallel
        const [salaryDetails, salaryArrears, roundOffSetting] = await Promise.all([
            getSalaryDetails(organizationDbConnection, args.employeeId),
            getRevisionDetails(organizationDbConnection, args.revisionId),
            getRoundOffSettings(organizationDbConnection)
        ]);

        roundOffSettings = roundOffSetting;

        // Validate retrieved data
        validateSalaryData(salaryArrears, salaryDetails);

        if (salaryArrears['Salary_Effective_Month']) {
            salaryArrears['Effective_Month'] = moment(salaryArrears['Salary_Effective_Month'], 'M,YYYY').format('YYYY-MM');
        } else if (salaryArrears['Effective_From']) {
            salaryArrears['Effective_Month'] = moment(salaryArrears['Effective_From'], 'YYYY-MM-DD').format('YYYY-MM');
        }
        salaryArrears['Payout_Month'] = moment(salaryArrears.Payout_Month, 'M,YYYY').subtract(1, 'month').format('YYYY-MM');

        // Step 2: Calculate month range and get payslips
        const monthList = getMonthsBetweenDates(
            salaryArrears.Effective_Month,
            salaryArrears.Payout_Month
        );

        const { salaryPayslips, resignationDate } = await getSalaryPayslipWithResignation(
            organizationDbConnection,
            args.employeeId,
            monthList
        );

        if(!salaryPayslips?.length){
            throw new Error('No salary payslips found for the given employee ID and month list');
        }

        // Step 3: Calculate allowance and retiral differences in parallel
        const [allowanceDifferences, retiralDifferences] = await Promise.all([
            calculateAllowanceDifferences(
                organizationDbConnection,
                salaryDetails,
                salaryArrears
            ),
            calculateRetiralDifferences(
                organizationDbConnection,
                salaryDetails,
                salaryArrears
            )
        ]);

        let allowanceArrearDetails = [];
        let retiralArrearDetails = [];

        retiralDifferences.forEach((item) => {
            let componentEmployerAmount = 0;
            let componentEmployeeAmount = 0;
            for (let i = 0; i < salaryPayslips.length; i++) {
                if (salaryPayslips[i].Unpaid_Leave_Days === 0) {
                    componentEmployerAmount += item.Employer_Share_Amount;
                    componentEmployeeAmount += item.Employee_Share_Amount;
                } else {
                    //Calculate the retiral amount based on the unpaid leave days
                    componentEmployerAmount += item.Employer_Share_Amount - (item.Employer_Share_Amount * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                    componentEmployeeAmount += item.Employee_Share_Amount - (item.Employee_Share_Amount * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                }
            }
            retiralArrearDetails.push({
                Revision_Id: args.revisionId,
                Form_Id: item.Form_Id,
                Retirals_Id: item.Retirals_Id,
                Employee_Share_Amount: componentEmployeeAmount,
                Employer_Share_Amount: componentEmployerAmount
            });
        });

        allowanceDifferences.forEach((item) => {
            let allowanceAmount = 0;
            for (let i = 0; i < salaryPayslips.length; i++) {
                if (salaryPayslips[i].Unpaid_Leave_Days === 0) {
                    allowanceAmount += item.Difference;
                } else {
                    //Calculate the allowance amount based on the unpaid leave days
                    allowanceAmount += item.Difference - (item.Difference * salaryPayslips[i].Unpaid_Leave_Days) / salaryPayslips[i].Salary_Calc_Tot_Working_Days;
                }
            }
            allowanceArrearDetails.push({
                Revision_Id: args.revisionId,
                Component_Name: 'Allowances',
                Component_Id: item.Allowance_Id,
                Component_Amount: allowanceAmount
            });
        })

        console.log('retiralArrearDetails', retiralArrearDetails)
        console.log('allowanceArrearDetails', allowanceArrearDetails)

        //Insert retiral arrear details and 
        await organizationDbConnection.transaction(async trx => {
            await Promise.all([
                organizationDbConnection(ehrTables.payslipRetiralsRevisionDetails)
                    .insert(retiralArrearDetails)
                    .transacting(trx),
                organizationDbConnection(ehrTables.revisionPayslipDetails)
                    .insert(allowanceArrearDetails)
                    .transacting(trx)
            ]);
        });

        return {
            errorCode: '',
            message: 'Salary arrears calculated successfully',
        };

    } catch (err) {
        console.error('Error in calculateSalaryArrears function:', err);
        const errResult = commonLib.func.getError(err, 'SLR0002');
        throw new ApolloError(errResult.message, errResult.code);
    } finally {
        if (organizationDbConnection &&  !context?.orgdb) {
            await organizationDbConnection.destroy();
        }
    }
};

/**
 * Retrieves the salary details for the given employee ID.
 * @param {Object} db - The database connection object.
 * @param {number} employeeId - The employee ID to retrieve salary details for.
 * @returns {Promise<Object>} - The salary details.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getSalaryDetails(db, employeeId) {
    try {
        return db(ehrTables.employeeSalaryDetails + ' as ES')
            .select('ES.*', 'ESL.Allowance_Id', 'ESL.Amount as Basic_Pay')
            .innerJoin(ehrTables.employeeSalaryAllowance + ' as ESL', 'ES.Employee_Id', 'ESL.Employee_Id')
            .innerJoin(ehrTables.allowances + ' as A', 'ESL.Allowance_Id', 'A.Allowance_Id')
            .innerJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .where('AT.Is_Basic_Pay', 'Yes')
            .where('ES.Employee_Id', employeeId)
            .first();

    } catch (err) {
        console.error('Error in getSalaryDetails function:', err);
        throw err;
    }
}

/**
 * Retrieves the revision details for the given revision ID.
 * @param {Object} db - The database connection object.
 * @param {number} revisionId - The revision ID to retrieve details for.
 * @returns {Promise<Object>} - The revision details.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getRevisionDetails(db, revisionId) {
    try {
        return db(ehrTables.salaryRevisionDetails + ' as SRD')
            .select('SRD.*', 'SL.Allowance_Id', 'SL.Amount as Basic_Pay')
            .innerJoin(ehrTables.salaryRevisionAllowance + ' as SL', 'SRD.Revision_Id', 'SL.Revision_Id')
            .innerJoin(ehrTables.allowances + ' as A', 'SL.Allowance_Id', 'A.Allowance_Id')
            .innerJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .where('AT.Is_Basic_Pay', 'Yes')
            .where('SRD.Revision_Id', revisionId)
            .first();
    } catch (err) {
        console.error('Error in getRevisionDetails function:', err);
        throw err;
    }
}

/**
 * Retrieves the salary payslips for the given employee ID and month list.
 * @param {Object} db - The database connection object.
 * @param {number} employeeId - The employee ID to retrieve salary payslips for.
 * @param {Array<string>} monthList - The list of month-year strings to retrieve salary payslips for.
 * @returns {Promise<Array>} - An array of salary payslips.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getSalaryPayslipWithResignation(db, employeeId, monthList) {
    try {
        if (monthList.length === 0) return [];

        const [salaryPayslips, resignationData] = await Promise.all([
            db(ehrTables.salaryPayslip + ' as SP')
                .select('SP.*', 'EJ.Date_Of_Join')
                .innerJoin(ehrTables.empJob + ' as EJ', 'SP.Employee_Id', 'EJ.Employee_Id')
                .where('SP.Employee_Id', employeeId)
                .whereIn('SP.Salary_Month', monthList),

            db(ehrTables.empResignation)
                .select('Resignation_Date')
                .where('Approval_Status', 'Approved')
                .where('Employee_Id', employeeId)
                .first()
        ]);

        if (resignationData) {
            //Check if the resignation comes between the monthList
            const resignationMonth = moment(resignationData.Resignation_Date).format('M,YYYY');
            const resignationIndex = monthList.indexOf(resignationMonth);
            if (resignationIndex !== -1) {
                monthList.splice(resignationIndex, 1);
            }
        }

        return {
            salaryPayslips,
            resignationDate: resignationData?.Resignation_Date
        };
    } catch (err) {
        console.error('Error in getSalaryPayslipWithResignation function:', err);
        throw err;
    }
}

// Validation function
function validateSalaryData(salaryArrears, salaryDetails) {
    if (!salaryArrears?.Effective_From || !salaryArrears?.Payout_Month) {
        console.log('Invalid data:', { salaryArrears });
        throw new Error('Salary details or arrears not found or incomplete');
    }
    if (!salaryDetails?.Basic_Pay) {
        console.log('Invalid data:', { salaryDetails });
        throw new Error('Salary details or arrears not found or incomplete');
    }
}

/**
 * Calculates the differences between the current and arrear allowances.
 * @param {Object} db - The database connection object.
 * @param {Object} salaryDetails - The salary details object.
 * @param {Object} salaryArrears - The salary arrears object.
 * @returns {Promise<Array>} - An array of allowance differences.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function calculateAllowanceDifferences(db, salaryDetails, salaryArrears) {
    try {
        const [currentAllowances, arrearAllowances] = await Promise.all([
            getAllowances(db, 'current', salaryDetails.Employee_Id),
            getAllowances(db, 'arrear', salaryArrears.Revision_Id)
        ]);

        const currentDetails = calculateAllowanceDetails(currentAllowances, salaryDetails.Basic_Pay);
        const arrearDetails = calculateAllowanceDetails(arrearAllowances, salaryArrears.Basic_Pay);

        return generateAllowanceDifferences(currentDetails, arrearDetails);
    } catch (err) {
        console.error('Error in calculateAllowanceDifferences function:', err);
        throw err;
    }
}

/**
 * Calculates the differences between the current and arrear retiral details.
 * @param {Object} db - The database connection object.
 * @param {Object} salaryDetails - The salary details object.
 * @param {Object} salaryArrears - The salary arrears object.
 * @returns {Promise<Array>} - An array of retiral differences.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function calculateRetiralDifferences(db, salaryDetails, salaryArrears) {
    try {
        const [currentRetirals, arrearRetirals] = await Promise.all([
            getCurrentRetirals(db, salaryDetails.Employee_Id),
            getArrearRetirals(db, salaryArrears.Revision_Id)
        ]);

        const currentDetails = calculateRetiralDetails(currentRetirals, salaryDetails.Basic_Pay);
        const arrearDetails = calculateRetiralDetails(arrearRetirals, salaryArrears.Basic_Pay);

        return generateRetiralDifference(arrearDetails, currentDetails);
    } catch (err) {
        console.error('Error in calculateRetiralDifferences function:', err);
        throw err;
    }
}

// Get allowances with optimized query
async function getAllowances(db, type, id) {
    try {
        const baseQuery = db
            .select('ESL.Allowance_Id', 'ESL.Amount', 'ESL.Percentage', 'ESL.Allowance_Type')
            .leftJoin(ehrTables.allowances + ' as A', 'ESL.Allowance_Id', 'A.Allowance_Id')
            .leftJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .where('AT.Allowance_Mode', 'Non Bonus');

        if (type === 'current') {
            return baseQuery
                .from(ehrTables.employeeSalaryAllowance + ' as ESL')
                .where('ESL.Employee_Id', id);
        } else {
            return baseQuery
                .from(ehrTables.salaryRevisionAllowance + ' as ESL')
                .where('ESL.Revision_Id', id);
        }
    } catch (err) {
        console.error('Error in getAllowances function:', err);
        throw err;
    }
}

/**
 * Retrieves the current retirals from the database for the given employee salary ID.
 * @param {Object} db - The database connection object.
 * @param {number} employeeSalaryId - The employee salary ID to retrieve current retirals for.
 * @returns {Promise<Array>} - An array of current retirals.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getCurrentRetirals(db, employeeSalaryId) {
    try {
        return db(ehrTables.employeeSalaryRetirals)
            .select('*')
            .where('Employee_Id', employeeSalaryId);
    } catch (err) {
        console.error('Error in getCurrentRetirals function:', err);
        throw err;
    }
}

/**
 * Retrieves the arrear retirals from the database for the given revision ID.
 * @param {Object} db - The database connection object.
 * @param {number} revisionId - The revision ID to retrieve arrear retirals for.
 * @returns {Promise<Array>} - An array of arrear retirals.
 * @throws Will log an error and throw if an error occurs during processing.
 */
async function getArrearRetirals(db, revisionId) {
    try {
        return db(ehrTables.salaryRevisionRetirals)
            .select('*')
            .where('Revision_Id', revisionId);
    } catch (err) {
        console.error('Error in getArrearRetirals function:', err);
        throw err;
    }
}

/**
 * Generates the difference between the arrear and existing allowance details.
 * @param {Array} currentDetails - Array of current allowance details.
 * @param {Array} arrearDetails - Array of arrear allowance details.
 * @returns {Array} - Array of allowance difference details.
 * @throws Will log an error and throw if an error occurs during processing.
 */
function generateAllowanceDifferences(currentDetails, arrearDetails) {
    try {
        const currentMap = new Map(currentDetails.map(a => [a.Allowance_Id, a]));
        const arrearMap = new Map(arrearDetails.map(a => [a.Allowance_Id, a]));
        const allIds = new Set([...currentMap.keys(), ...arrearMap.keys()]);

        return Array.from(allIds).map(id => {
            const current = currentMap.get(id);
            const arrear = arrearMap.get(id);

            let difference = 0;
            if (current && arrear) {
                difference = arrear.Amount - current.Amount;
            } else if (arrear) {
                difference = arrear.Amount;
            } else if (current) {
                difference = -current.Amount;
            }

            return {
                Allowance_Id: id,
                Difference: getRoundOffValue(formId.allowances, difference, roundOffSettings)
            };
        }).filter(item => Math.abs(item.Difference) > 0.01); // Filter out negligible differences
    } catch (err) {
        console.error('Error in generateAllowanceDifferences function:', err);
        throw err;
    }
}

/**
 * Generates the difference between the arrear and existing retiral details.
 * @param {Array} arrear - Array of arrear retiral details.
 * @param {Array} existing - Array of existing retiral details.
 * @returns {Array} - Array of retiral difference details.
 * @throws Will log an error and throw if an error occurs during processing.
 */
function generateRetiralDifference(arrear = [], existing = []) {
    try {
        const existingMap = new Map(
            existing.map(item => [`${item.Form_Id}_${item.Retirals_Id}`, item])
        );

        const result = [];
        const processedKeys = new Set();

        // Process arrear items
        for (const item of arrear) {
            const key = `${item.Form_Id}_${item.Retirals_Id}`;
            const existingItem = existingMap.get(key);

            const employeeDiff = item.Employee_Share_Amount - (existingItem?.Employee_Share_Amount || 0);
            const employerDiff = item.Employer_Share_Amount - (existingItem?.Employer_Share_Amount || 0);

            if (Math.abs(employeeDiff) > 0.01 || Math.abs(employerDiff) > 0.01) {
                result.push({
                    Form_Id: item.Form_Id,
                    Revision_Id: item.Revision_Id,
                    Retirals_Id: item.Retirals_Id,
                    Employee_Share_Amount: getRoundOffValue(item.Form_Id, employeeDiff, roundOffSettings),
                    Employer_Share_Amount: getRoundOffValue(item.Form_Id, employerDiff, roundOffSettings)
                });
            }

            processedKeys.add(key);
        }

        // Process remaining existing items
        for (const item of existing) {
            const key = `${item.Form_Id}_${item.Retirals_Id}`;
            if (!processedKeys.has(key)) {
                result.push({
                    Form_Id: item.Form_Id,
                    Revision_Id: item.Revision_Id,
                    Retirals_Id: item.Retirals_Id,
                    Employee_Share_Amount: getRoundOffValue(item.Form_Id, -item.Employee_Share_Amount, roundOffSettings),
                    Employer_Share_Amount: getRoundOffValue(item.Form_Id, -item.Employer_Share_Amount, roundOffSettings)
                });
            }
        }

        return result;
    } catch (err) {
        console.error('Error in generateRetiralDifference:', err);
        throw err;
    }
}

/**
 * Calculates retiral amounts based on the given retiral details and basic pay.
 * If the retiral type is 'percentage', calculates the amount as a percentage of basic pay.
 * @param {Array} retirals - Array of retiral objects.
 * @param {number} basicPay - The basic pay amount for calculation.
 * @returns {Array} - Array of retiral details with calculated amounts if applicable.
 * @throws Will log an error and throw if an error occurs during processing.
 */
function calculateRetiralDetails(retirals = [], basicPay = 0) {
    try {
        if (!Array.isArray(retirals) || basicPay <= 0) {
            return retirals;
        }

        return retirals.map(retiral => {
            if (retiral.Retirals_Type?.toLowerCase() === 'percentage') {
                const employerPercentage = parseFloat(retiral.Employer_Share_Percentage) || 0;
                const employeePercentage = parseFloat(retiral.Employee_Share_Percentage) || 0;

                return {
                    ...retiral,
                    Employer_Share_Amount: (basicPay * employerPercentage) / 100,
                    Employee_Share_Amount: (basicPay * employeePercentage) / 100
                };
            }
            return retiral;
        });
    } catch (err) {
        console.error('Error in calculateRetiralDetails function:', err);
        throw err;
    }
}

/**
 * Returns an array of month-year strings between the given start and end month-year.
 * The start and end month-year are inclusive.
 * The function throws an error if the input format is invalid or if the start date is after the end date.
 * @param {string} startMonthYear - The start month-year in the format 'month-year', e.g. '01-2022'.
 * @param {string} endMonthYear - The end month-year in the format 'month-year', e.g. '12-2022'.
 * @returns {Array<string>} - An array of month-year strings between the given start and end month-year.
 * @throws {Error} - If the input format is invalid or if the start date is after the end date.
 */
function getMonthsBetweenDates(startMonthYear, endMonthYear) {
    try {
        if (!startMonthYear || !endMonthYear) {
            throw new Error('Start and end month-year are required');
        }

        const parseMonthYear = (monthYear) => {
            const [year, month] = monthYear.split('-').map(num => parseInt(num.trim()));
            if (isNaN(month) || isNaN(year) || month < 1 || month > 12) {
                throw new Error(`Invalid month-year format: ${monthYear}`);
            }
            return { month, year };
        };

        const start = parseMonthYear(startMonthYear);
        const end = parseMonthYear(endMonthYear);

        const months = [];
        let current = { ...start };

        while (
            current.year < end.year ||
            (current.year === end.year && current.month <= end.month)
        ) {
            months.push(`${current.month},${current.year}`);

            current.month++;
            if (current.month > 12) {
                current.month = 1;
                current.year++;
            }
        }

        return months;
    } catch (err) {
        console.error('Error in getMonthsBetweenDates:', err);
        throw err;
    }
}

/**
 * Calculates allowance amounts based on the given allowances and basic pay.
 * If the allowance type is 'percentage', calculates the amount as a percentage of basic pay.
 * @param {Array} allowances - Array of allowance objects.
 * @param {number} basicPay - The basic pay amount for calculation.
 * @returns {Array} - Array of allowances with calculated amounts if applicable.
 * @throws Will log an error and throw if an error occurs during processing.
 */
function calculateAllowanceDetails(allowances = [], basicPay = 0) {
    try {
        if (!Array.isArray(allowances) || basicPay <= 0) {
            return allowances;
        }

        return allowances.map(allowance => {
            if (allowance.Allowance_Type?.toLowerCase() === 'percentage') {
                const percentage = parseFloat(allowance.Percentage) || 0;
                return {
                    ...allowance,
                    Amount: (basicPay * percentage) / 100
                };
            }
            return allowance;
        });
    } catch (err) {
        console.error('Error in calculateAllowanceDetails function:', err);
        throw err;
    }
}