const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ApolloError } = require('apollo-server-lambda');
const { ehrTables } = require('../../common/tablealias');
const { formId } = require('../../common/appconstants');

let roundOffSettings;
let organizationDbConnection;
module.exports.calculateSalary = async (parent, args, context, info) => {
    try {
        organizationDbConnection = context.orgdb ? context.orgdb : knex(context.connection.OrganizationDb);

        let employeeRetiralDetails = {}

        //Step 1: Retrieve the input data
        const allowance = args.allowanceDetails?.length > 0 ? JSON.parse(args.allowanceDetails) : []
        const retiralDetails = args.retiralDetails?.length > 0 ? JSON.parse(args.retiralDetails) : []
        const salaryDetails = args.salaryDetails ? JSON.parse(args.salaryDetails) : {}

        //Step 2: Retrieve the allowance details, round off settings, salary configuration and payroll general settings
        let [allowanceDetails, roundOff, salaryConfiguration, payrollGeneralSettings] = await Promise.all([
            getAllowanceDetails(organizationDbConnection, allowance),
            getRoundOffSettings(),
            retrieveSalaryConfiguration(organizationDbConnection, args.employeeId),
            retrievePayrollGeneralSettings(organizationDbConnection)
        ]);

        roundOffSettings = roundOff;
        const basicPayEarning = allowanceDetails.find(item => item.Is_Basic_Pay?.toLowerCase() === 'yes');
        if (basicPayEarning) {
            basicPayEarning.Amount = allowance.find(item => item.Allowance_Id === basicPayEarning.Allowance_Id)?.Amount;
            // allowanceDetails = allowanceDetails.filter(item => item.Allowance_Id !== basicPayEarning.Allowance_Id);
        }

        //Step 3: Calculate monthly CTC (Cost to Company)
        const annualCTC = getRoundOffValue(formId.salary, salaryDetails.Annual_Ctc);
        const monthlyCTC = getRoundOffValue(formId.salary, annualCTC / 12);

        //Step 4: Calculate Basic Pay
        const basicPay = basicPayEarning ? getRoundOffValue(formId.salary, basicPayEarning.Amount) : 0;

        //Step 5: Calculate Allowance Amount and Total Allowance Amount
        const allowanceData = calculateAllowanceDetails(allowanceDetails, basicPay);

        const totalAllowanceAmount = allowanceData.reduce((total, allowance) => {
            return total + (allowance.Amount || 0);
        }, 0);

        //Step 6: Calculate PF
        let providentFundSettings = null;
        let socialSecurityScheme = null;

        const providentFundDetailsRaw = retiralDetails?.find(item => item.Form_Id === formId.pfId);

        let providentFundDetails = null;

        if (providentFundDetailsRaw) {
            const [settings, config, scheme] = await Promise.all([
                retrieveProvidentFundSettings(organizationDbConnection),
                retrieveProvidentFundConfiguration(organizationDbConnection),
                retrieveSocialSecurityScheme(organizationDbConnection)
            ]);

            providentFundSettings = settings;
            socialSecurityScheme = scheme;
            providentFundDetails = { ...providentFundDetailsRaw, ...config };
        }


        // Step 7: Calculate Insurance
        let insuranceType = null;
        const insuranceDetails = retiralDetails?.filter(item => item.Form_Id === formId.insurance) || [];

        if (salaryConfiguration?.Eligible_For_Insurance && insuranceDetails.length > 0) {
            const tableInsuranceDetails = await organizationDbConnection(ehrTables.insuranceConfiguration)
                .select('*')
                .whereIn('InsuranceType_Id', insuranceDetails.map(item => item.Retirals_Id));

            const userInsuranceData = commonLib.func.organizeData(insuranceDetails, 'Retirals_Id');

            insuranceType = tableInsuranceDetails.map(insuranceDetail => {
                const currentRecord = ensureArray(userInsuranceData, insuranceDetail.InsuranceType_Id)[0];
                return {
                    ...insuranceDetail,
                    Employee_Share_Percentage: currentRecord.Employee_Share_Percentage,
                    Employer_Share_Percentage: currentRecord.Employer_Share_Percentage,
                    Employee_Share_Amount: currentRecord.Employee_Share_Amount,
                    Employer_Share_Amount: currentRecord.Employer_Share_Amount,
                    Form_Id: currentRecord.Form_Id
                };
            });
        }

        // Step 8: Calculate NPS
        let npsSlabDetails = null
        const npsRetirals = retiralDetails?.length > 0 ?
            retiralDetails.find((item) => item.Form_Id === formId.npsId) : null;

        if (npsRetirals && salaryConfiguration?.Eligible_For_Nps) {
            npsSlabDetails = await organizationDbConnection(ehrTables.npsSlab)
                .select('*')
        }

        // Step 9: Calculate Gratuity
        let gratuitySettings = null
        const gratuityRetirals = retiralDetails?.length > 0 ?
            retiralDetails.find((item) => item.Form_Id === formId.gratuityId) : null;

        if (salaryConfiguration?.Eligible_For_Gratuity && gratuityRetirals) {
            gratuitySettings = await retrieveGratuitySettings(organizationDbConnection)
        }

        // Step 10: Calculate Bonus
        let bonusAllowanceDetails = allowanceDetails.filter((item) => item.Allowance_Mode?.toLowerCase() === 'bonus');

        // Step 11: Calculate Fixed Allowance (temporarily 0 to calculate PF and Insurance properly)
        let fixedAllowance = 0;
        let pfEmployerShareAmount = 0;
        let insuranceEmployerShareAmount = 0;
        let npsEmployerShareAmount = 0;
        let gratuityAmount = 0;
        let bonusAmount = 0;
        let finalAllowanceAmount = 0;

        // Helper: Calculates PF and returns { response, employerAmount }
        const getProvidentFund = async () => {
            if (!providentFundDetails || !salaryConfiguration?.Eligible_For_Pf) return { response: null, employerAmount: 0 };
            const response = await calculateProvidentFundDetails(
                providentFundDetails,
                basicPay,
                allowanceData,
                providentFundSettings,
                socialSecurityScheme,
                payrollGeneralSettings
            );
            return { response, employerAmount: response?.Employer_Share_Amount || 0 };
        };

        // Helper: Calculates Gratuity
        const getGratuity = async () => {
            if (!gratuityRetirals || !gratuitySettings || !salaryConfiguration?.Eligible_For_Gratuity) return { response: null, employerAmount: 0 };
            const response = await calculateGratuityAmount(
                basicPay,
                gratuityRetirals,
                allowanceData,
                gratuitySettings
            );
            return { response, employerAmount: response?.Employer_Share_Amount || 0 };
        };

        // Helper: Calculates NPS
        const getNPS = async () => {
            if (!npsSlabDetails || !salaryConfiguration?.Eligible_For_Nps) return { response: null, employerAmount: 0 };
            const response = await calculateNPSDetails(
                allowanceData,
                basicPay,
                npsRetirals,
                npsSlabDetails,
                payrollGeneralSettings
            );
            return { response, employerAmount: response?.Employer_Share_Amount || 0 };
        };

        // Helper: Calculates Insurance
        const getInsurance = async () => {
            if (!insuranceDetails?.length || !salaryConfiguration?.Eligible_For_Insurance) return { response: [], employerAmount: 0 };
            const response = await calculateInsuranceDetails(
                organizationDbConnection,
                insuranceType,
                allowanceData,
                basicPay,
                salaryDetails
            );
            const employerAmount = response.reduce((total, ins) => total + ins.Employer_Share_Amount, 0);
            return { response, employerAmount };
        };

        // Helper: Calculates Bonus
        const getBonus = () => {
            if (!bonusAllowanceDetails?.length) return { response: [], amount: 0 };
            const response = calculateBonusDetails(
                bonusAllowanceDetails,
                allowanceData,
                basicPay
            );
            const amount = response.reduce((total, b) => total + (b.Amount || 0), 0);
            return { response, amount };
        };

        // Step 12: --- Iterative convergence loop ---
        for (let i = 0; i < 10; i++) {
            const [pf, gratuity, nps, insurance] = await Promise.all([
                getProvidentFund(),
                getGratuity(),
                getNPS(),
                getInsurance()
            ]);
            const bonus = getBonus();

            pfEmployerShareAmount = pf.employerAmount;
            gratuityAmount = gratuity.employerAmount;
            npsEmployerShareAmount = nps.employerAmount;
            insuranceEmployerShareAmount = insurance.employerAmount;
            bonusAmount = bonus.amount;

            // Recalculate fixed allowance
            fixedAllowance = monthlyCTC - (
                basicPay +
                totalAllowanceAmount +
                pfEmployerShareAmount +
                insuranceEmployerShareAmount +
                npsEmployerShareAmount +
                gratuityAmount +
                bonusAmount
            );

            fixedAllowance = Math.max(0, fixedAllowance);

            for (let allowance of allowanceData) {
                if (allowance.Formula_Based?.toLowerCase() === 'yes') {
                    allowance.Amount = fixedAllowance;
                }
            }
        }

        // Step 13: Final round of calculations (accurate values after convergence)
        const [finalPf, finalGratuity, finalNps, finalInsurance] = await Promise.all([
            getProvidentFund(),
            getGratuity(),
            getNPS(),
            getInsurance()
        ]);
        const finalBonus = getBonus();

        pfEmployerShareAmount = finalPf.employerAmount;
        gratuityAmount = finalGratuity.employerAmount;
        npsEmployerShareAmount = finalNps.employerAmount;
        insuranceEmployerShareAmount = finalInsurance.employerAmount;
        bonusAmount = finalBonus.amount;

        // Step 14: Prepare employeeRetiralDetails
        let retirals = [];
        if (finalPf.response) retirals.push(finalPf.response);
        if (finalGratuity.response) retirals.push(finalGratuity.response);
        if (finalNps.response) retirals.push(finalNps.response);
        if (finalInsurance.response.length) retirals.push(...finalInsurance.response);
        employeeRetiralDetails['employeeSalaryRetirals'] = retirals;


        if (finalBonus.response.length) employeeRetiralDetails['employeeSalaryBonus'] = finalBonus.response;
        if (allowanceData.length) {
            let finalAllowances = allowanceData.map((item) => {
                return {
                    Allowance_Id: item.Allowance_Id,
                    Allowance_Type: item.Allowance_Type,
                    Percentage: item.Percentage,
                    Amount: item.Amount,
                    Allowance_Wage: item.Allowance_Wage
                }
            });
            if (basicPayEarning) {
                let basicPayAllowance = {
                    Allowance_Id: basicPayEarning.Allowance_Id,
                    Allowance_Type: basicPayEarning.Allowance_Type,
                    Percentage: basicPayEarning.Percentage,
                    Amount: basicPayEarning.Amount,
                    Allowance_Wage: basicPayEarning.Allowance_Wage
                }
                finalAllowances.push(basicPayAllowance)
            }
            employeeRetiralDetails['employeeSalaryAllowance'] = finalAllowances
        }

        // Step 15: Calculate finalAllowanceAmount
        finalAllowanceAmount = allowanceData.reduce((sum, a) => sum + (a.Amount || 0), 0);

        // Step 16: Calculate calculatedTotal
        const calculatedTotal = basicPay + finalAllowanceAmount + pfEmployerShareAmount +
            insuranceEmployerShareAmount + npsEmployerShareAmount + gratuityAmount + bonusAmount;

        const salaryStructure = {
            basic: basicPay,
            fixedAllowance,
            finalAllowanceAmount,
            pfEmployerShareAmount,
            insuranceEmployerShareAmount,
            npsEmployerShareAmount,
            gratuityAmount,
            bonusAmount,
            total: calculatedTotal,
            monthlyCTC
        };

        let errorCode = '';
        if (calculatedTotal > monthlyCTC) errorCode = 'PST0110';

        return {
            errorCode,
            message: errorCode ? 'Total calculated salary exceeds the annual CTC. Please review the salary structure.' : 'Salary calculated successfully',
            employeeRetiralDetails: JSON.stringify(employeeRetiralDetails),
            salaryStructure: JSON.stringify(salaryStructure)
        };


    } catch (err) {
        console.error('Error in calculateSalary function main catch block.', err);
        let errResult = commonLib.func.getError(err, 'PFF0020');
        throw new ApolloError(errResult.message, errResult.code)
    }
    finally {
        if (!context.orgdb) organizationDbConnection ? organizationDbConnection.destroy() : null
            ;
    }
}

/**
 * Retrieves the provident fund configuration from the database for a given organization.
 * @param {Object} organizationDbConnection - Database connection object for the organization's database.
 * @returns {Promise<Object>} - Promise resolving to the provident fund configuration.
 * @throws Will throw an error if the database query fails.
 */
async function retrieveProvidentFundConfiguration(organizationDbConnection) {
    try {
        let providentFundConfiguration = await organizationDbConnection(ehrTables.providentFund)
            .select('*')
            .first()

        return providentFundConfiguration
    } catch (err) {
        console.error('Error in retrieveProvidentFundConfiguration function', err);
        throw err
    }
}

/**
 * Processes provident fund details and calculates contributions
 * @param {Object} options - Configuration options
 * @param {Object} options.retiralDetails - Employee's retiral details
 * @param {Object} options.salaryDetails - Employee's salary details
 * @param {Array} options.allowanceDetails - Employee's allowance details
 * @param {Object} options.providentFundSettings - Provident fund settings
 * @param {Object} options.socialSecurityScheme - Social security scheme details
 * @param {Object} options.payrollGeneralSettings - General payroll settings
 * @returns {Promise<Object>} - Processed provident fund details
 */
async function calculateProvidentFundDetails(
    providentFundDetails,
    basicPay,
    allowanceDetails,
    providentFundSettings,
    socialSecurityScheme,
    payrollGeneralSettings
) {
    try {
        const employeePfWage = getPfSalary(
            basicPay,
            allowanceDetails,
            providentFundSettings,
            providentFundDetails,
            basicPay
        );

        let providentFundResponse;
        if (payrollGeneralSettings?.Slab_Wise_PF?.toLowerCase() === 'yes') {
            providentFundResponse = await calculateSlabWisePf(socialSecurityScheme, employeePfWage);
        } else {
            providentFundResponse = await calculateCurrentProvidentFundDetails(
                employeePfWage,
                providentFundSettings,
                providentFundDetails
            );
        }

        return {
            'Retirals_Id': providentFundResponse.Retirals_Id || providentFundDetails.Retirals_Id,
            'Form_Id': providentFundResponse.Form_Id || providentFundDetails.Form_Id,
            'Employee_Retiral_Wages': providentFundResponse.Employee_Retiral_Wages,
            'Employer_Retiral_Wages': providentFundResponse.Employer_Retiral_Wages,
            'Employee_Share_Amount': providentFundResponse.Employee_Share_Amount,
            'Employer_Share_Amount': providentFundResponse.Employer_Share_Amount,
            'Employer_Share_Percentage': providentFundDetails.Employer_Share_Percentage,
            'Employee_Share_Percentage': providentFundDetails.Employee_Share_Percentage,
            'Admin_Charge': providentFundResponse.Admin_Edli_Charges?.Employee_Admin_Charge || 0,
            'EDLI_Charge': providentFundResponse.Admin_Edli_Charges?.Employee_Edli_Charge || 0,
            'Retiral_Type': providentFundDetails?.Retirals_Type
        };
    } catch (error) {
        console.error('Error processing provident fund details:', error);
        throw error;
    }
}

/**
 * Calculates bonus details based on the given allowance details and basic pay.
 * @param {Array} bonusAllowanceDetails - Array of bonus allowance details.
 * @param {Array} allowanceDetails - Array of allowance details.
 * @param {number} basicPay - Basic pay amount.
 * @returns {Array} - Array of bonus details.
 */
function calculateBonusDetails(bonusAllowanceDetails, allowanceDetails, basicPay) {
    try {
        const bonusFormId = formId.bonus.toString();
        const bonusWages = getStatutorySalary(allowanceDetails, basicPay, bonusFormId);
        if (bonusAllowanceDetails?.length > 0) {
            for (bonus of bonusAllowanceDetails) {
                bonus.Amount = calculateAllowanceAmount(bonusWages, bonus)
            }
        }

        let formAllowanceData = bonusAllowanceDetails.map((item) => {
            return {
                Allowance_Id: item.Allowance_Id,
                Allowance_Type: item.Allowance_Type,
                Percentage: item.Percentage,
                Amount: item.Amount,
                Allowance_Wage: item.Allowance_Type?.toLowerCase() === 'percentage' ? bonusWages : null
            }
        })
        return formAllowanceData;
    }
    catch (error) {
        console.error('Error in calculateBonusDetails function', error);
        throw error;
    }
}

/**
 * Calculates allowance details based on the given allowance details and basic pay.
 * @param {Array} allowanceDetails - Array of allowance details.
 * @param {number} basicPay - Basic pay amount.
 * @returns {Array} - Array of allowance details.
 */
function calculateAllowanceDetails(allowanceDetails, basicPay) {
    try {
        allowanceDetails = allowanceDetails.filter((item) => item.Allowance_Mode?.toLowerCase() === 'non bonus' && item.Is_Basic_Pay?.toLowerCase() === 'no')
        if (allowanceDetails?.length > 0) {
            for (allowance of allowanceDetails) {
                //We will calculate the Formula Based allowance at the end
                if (allowance.Formula_Based?.toLowerCase() === 'yes') {
                    allowance.Amount = 0;
                    allowance.Allowance_Wage = 0;
                } else {
                    if (allowance.Allowance_Type?.toLowerCase() === 'percentage') {
                        let allowanceAmount = (basicPay * parseFloat(allowance.Percentage)) / 100;
                        allowance.Amount = allowanceAmount;
                        allowance.Allowance_Wage = basicPay;
                    } else {
                        allowance.Allowance_Wage = 0;
                    }
                    //For Allowance Type is Amount it will be as it is
                }
            }
        }
        return allowanceDetails;
    }
    catch (error) {
        console.error('Error in formAllowanceData:', error);
        throw error;
    }
}

/**
 * Processes insurance details and calculates employee and employer contributions
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} insuranceDetails - Array of insurance details
 * @param {Array} allowanceDetails - Array of allowance details
 * @param {number} basicPay - Employee's basic pay
 * @returns {Promise<Array>} - Array of processed insurance details
 */
async function calculateInsuranceDetails(
    organizationDbConnection,
    insuranceTypes,
    allowanceDetails,
    basicPay,
    salaryDetails
) {
    try {
        const employeeRetiralDetails = [];

        const fixedInsurance = insuranceTypes.filter(i => i.Insurance_Type?.toLowerCase() === 'fixed');
        const variableInsurance = insuranceTypes.filter(i => i.Insurance_Type?.toLowerCase() === 'variable');
        const slabWiseInsurance = insuranceTypes.filter(i => i.Slab_Wise_Insurance?.toLowerCase() === 'yes');

        const insuranceWages = (variableInsurance.length > 0 || slabWiseInsurance.length > 0)
            ? getStatutorySalary(allowanceDetails, basicPay, formId.variableInsurance.toString())
            : 0;

        const createInsuranceDetail = ({
            InsuranceType_Id,
            Form_Id,
            Insurance_Name,
            Retiral_Wages,
            Employee_Share_Amount,
            Employer_Share_Amount,
            Employee_Share_Percentage = null,
            Employer_Share_Percentage = null,
            Retiral_Type
        }) => ({
            Retirals_Id: InsuranceType_Id,
            Form_Id,
            Insurance_Name,
            Retiral_Wages,
            Employee_Share_Amount: getRoundOffValue(formId.insurance, Employee_Share_Amount),
            Employer_Share_Amount: getRoundOffValue(formId.insurance, Employer_Share_Amount),
            Employee_Share_Percentage,
            Employer_Share_Percentage,
            Retiral_Type
        });

        // Fixed insurance processing
        for (const fixed of fixedInsurance) {
            employeeRetiralDetails.push(createInsuranceDetail({
                ...fixed,
                Employee_Retiral_Wages: null,
                Employer_Retiral_Wages: null,
                Employee_Share_Amount: fixed.Employee_Share_Amount,
                Employer_Share_Amount: fixed.Employer_Share_Amount,
                Retiral_Type: 'Fixed'
            }));
        }

        // Variable insurance processing
        for (const variable of variableInsurance) {
            const esiEligible = variable.Employee_State_Insurance?.toLowerCase() === 'yes';
            let includeVariable = true;

            if (esiEligible) {
                const config = await retrieveInsuranceContributionConfiguration(organizationDbConnection);
                includeVariable = (config && insuranceWages >= config.Min_Salary && insuranceWages <= config.Max_Salary) ||
                    (salaryDetails.Effective_From && salaryDetails.ESI_Contribution_End_Date);
            }

            if (includeVariable) {
                const empShare = (variable.Employee_Share_Percentage * insuranceWages) / 100;
                const emrShare = (variable.Employer_Share_Percentage * insuranceWages) / 100;

                employeeRetiralDetails.push(createInsuranceDetail({
                    ...variable,
                    Employee_Retiral_Wages: insuranceWages,
                    Employer_Retiral_Wages: insuranceWages,
                    Employee_Share_Amount: empShare,
                    Employer_Share_Amount: emrShare,
                    Employee_Share_Percentage: variable.Employee_Share_Percentage,
                    Employer_Share_Percentage: variable.Employer_Share_Percentage,
                    Retiral_Type: 'Variable'
                }));
            }
        }

        // Slab-wise insurance processing
        if (slabWiseInsurance.length > 0) {
            const philHealthSlabs = await organizationDbConnection(ehrTables.philHealthSlabs).select('*');

            for (const slab of slabWiseInsurance) {
                const slabDetails = calculateSlabWiseInsurance(philHealthSlabs, insuranceWages);
                if (!slabDetails) continue;

                employeeRetiralDetails.push(createInsuranceDetail({
                    ...slab,
                    Employee_Retiral_Wages: insuranceWages,
                    Employer_Retiral_Wages: insuranceWages,
                    Employee_Share_Amount: slabDetails.Employee_Share_Amount,
                    Employer_Share_Amount: slabDetails.Employer_Share_Amount,
                    Employee_Share_Percentage: slabDetails.Employee_Share_Percentage,
                    Employer_Share_Percentage: slabDetails.Employer_Share_Percentage,
                    Retiral_Type: 'Variable'
                }));
            }
        }

        return employeeRetiralDetails;
    } catch (error) {
        console.error('Error in calculateInsuranceDetails:', error);
        throw error;
    }
}


/**
 * Retrieves the insurance contribution configuration for a given insurance type
 * @param {Knex} organizationDbConnection - Knex connection to the organization database
 * @param {number} insuranceTypeId - ID of the insurance type to retrieve the configuration for
 * @returns {Object} Insurance contribution configuration object
 */
async function retrieveInsuranceContributionConfiguration(organizationDbConnection) {
    try {
        let insuranceContributionConfiguration = await organizationDbConnection(ehrTables.esiStatutoryConfiguration)
            .select('*')
            .first()

        return insuranceContributionConfiguration
    } catch (error) {
        console.error('Error in retrieveInsuranceContributionConfiguration', error);
        throw error
    }
}

/**
 * Retrieves the social security scheme details
 * @param {Knex} organizationDbConnection - Knex connection to the organization database
 * @returns {Array} Array of social security scheme details
 */
async function retrieveSocialSecurityScheme(organizationDbConnection) {
    try {
        let socialSecurityScheme = await organizationDbConnection(ehrTables.socialSecurityScheme)
            .select('*')

        return socialSecurityScheme
    } catch (err) {
        console.error('Error in retrieveSocialSecurityScheme', err);
        throw err
    }
}

/**
 * Retrieves the provident fund settings from the database for a given organization.
 * @param {Knex} organizationDbConnection - Knex connection to the organization's database.
 * @returns {Promise<Object>} - Promise resolving to the provident fund settings.
 * @throws Will throw an error if the database query fails.
 */
async function retrieveProvidentFundSettings(organizationDbConnection) {
    try {
        let providentFundSettings = await organizationDbConnection(ehrTables.providentFundSettings)
            .select('*')
            .first()

        return providentFundSettings
    } catch (err) {
        console.error('Error in retrieveProvidentFundSettings', err);
        throw err
    }
}

/**
 * Calculates slab-wise Provident Fund (PF) contributions based on employee's PF wage and social security scheme slabs
 * @param {Array} socialSecuritySchemeSlabs - Array of social security scheme slabs
 * @param {number} employeePfWage - Employee's PF wage
 * @returns {Object} - Object containing PF contribution details
 */
async function calculateSlabWisePf(socialSecuritySchemeSlabs, employeePfWage) {
    try {
        // Round off the employee's PF wage
        employeePfWage = getRoundOffValue(formId.pfId, employeePfWage);

        // Initialize default return values
        const providentFundDetails = {};
        const adminEdliCharges = {
            Employee_Admin_Charge: 0,
            Employee_Edli_Charge: 0
        };

        // Find the matching slab
        for (const slab of socialSecuritySchemeSlabs) {
            const rangeFrom = parseFloat(slab.Range_From) || 0;
            let rangeTo = parseFloat(slab.Range_To) || 9999999999999.99;

            if (employeePfWage >= rangeFrom && employeePfWage <= rangeTo) {
                // Parse slab values with defaults
                const medianValue = parseFloat(slab.Median_Value) || 0;
                const wisp = parseFloat(slab.WISP) || 0;

                // Parse percentages
                const regularSSEEPercentage = parseFloat(slab.Regular_SS_EE_Percentage) || 0;
                const wispEEPercentage = parseFloat(slab.WISP_EE_Percentage) || 0;
                const regularSSERPercentage = parseFloat(slab.Regular_SS_ER_Percentage) || 0;
                const wispERPercentage = parseFloat(slab.WISP_ER_Percentage) || 0;
                const ecEE = parseFloat(slab.EC_EE) || 0;
                const ecER = parseFloat(slab.EC_ER) || 0;

                // Calculate employee contributions
                const regularSSEE = (medianValue * regularSSEEPercentage) / 100;
                const wispEmployeeShare = (wisp * wispEEPercentage) / 100;
                const sumofEmployeeShare = regularSSEE + wispEmployeeShare + ecEE;

                // Calculate employer contributions
                const regularSSER = (medianValue * regularSSERPercentage) / 100;
                const wispEmployerShare = (wisp * wispERPercentage) / 100;
                const sumofEmployerShare = regularSSER + wispEmployerShare + ecER;

                // Build the result object with rounded values
                return {
                    Retirals_Id: providentFundDetails.Retirals_Id,
                    Form_Id: providentFundDetails.Form_Id,
                    Regular_SS_EE: getRoundOffValue(formId.pfId, regularSSEE),
                    WISP_EE: getRoundOffValue(formId.pfId, wispEmployeeShare),
                    EC_EE: getRoundOffValue(formId.pfId, ecEE),
                    Employee_Share_Amount: getRoundOffValue(formId.pfId, sumofEmployeeShare),
                    Regular_SS_ER: getRoundOffValue(formId.pfId, regularSSER),
                    WISP_ER: getRoundOffValue(formId.pfId, wispEmployerShare),
                    EC_ER: getRoundOffValue(formId.pfId, ecER),
                    Employer_Share_Amount: getRoundOffValue(formId.pfId, sumofEmployerShare),
                    Employee_Provident_Fund_Wage: employeePfWage,
                    Admin_Edli_Charges: {
                        Employee_Admin_Charge: 0,
                        Employee_Edli_Charge: 0
                    }
                };
            }
        }

        // Return empty values if no matching slab is found
        return {
            Regular_SS_EE: 0,
            WISP_EE: 0,
            EC_EE: 0,
            Employee_Share_Amount: 0,
            Regular_SS_ER: 0,
            WISP_ER: 0,
            EC_ER: 0,
            Employer_Share_Amount: 0,
            Employee_Provident_Fund_Wage: 0,
            Admin_Edli_Charges: adminEdliCharges
        };
    } catch (error) {
        console.error('Error in calculateSlabWisePf', error);
        throw error
    }
}


/**
 * Calculates the Provident Fund salary based on various conditions
 * @param {number} basicPay - Employee's basic pay
 * @param {Array} allowanceDetails - Array of allowance details
 * @param {Object} providentFundSettings - PF settings from database
 * @param {Object} providentFundDetails - Employee's PF details
 * @param {number} actualBasicPay - Employee's actual basic pay
 * @param {number} pfAdhocAllowanceAmount - Additional adhoc allowance amount (default: 0)
 * @returns {number} - Calculated PF salary
 */
function getPfSalary(basicPay, allowanceDetails, providentFundSettings, providentFundDetails, actualBasicPay, pfAdhocAllowanceAmount = 0) {
    try {
        let basicPlusAllowance = basicPay + (parseFloat(pfAdhocAllowanceAmount) || 0);
        const benefitFormId = formId.pfId.toString();
        const restrictedWage = providentFundSettings?.Restricted_PF_Wage_Amount || 15000;

        // Check if we should return basic + allowance immediately
        if (providentFundDetails?.Provident_Fund_Configuration?.toLowerCase() === 'current' &&
            actualBasicPay >= restrictedWage &&
            providentFundDetails?.PF_Calculated_As_Percentage_Of_Basic_Beyond_Statutory_Limit?.toLowerCase() === 'yes') {
            return basicPlusAllowance;
        }

        // Process allowances based on configuration
        if (allowanceDetails?.length > 0) {
            // First pass: Process allowances based on current configuration
            for (const allowance of allowanceDetails) {
                if (allowance?.BenefitForms?.split(',').includes(benefitFormId)) {
                    if (allowance.Consider_For_EPF_Contribution?.toLowerCase() === 'always') {
                        basicPlusAllowance += parseFloat(allowance.Amount || 0);
                    }
                }
            }

            // Second pass: Process LOP conditions if applicable
            if (providentFundDetails?.Provident_Fund_Configuration?.toLowerCase() === 'current' &&
                providentFundDetails?.Consider_All_Salary_Components_For_LOP?.toLowerCase() === 'yes') {

                for (const allowance of allowanceDetails) {
                    if (allowance?.BenefitForms?.split(',').includes(benefitFormId) &&
                        allowance.Consider_For_EPF_Contribution?.toLowerCase() === 'only when pf wage is less than ₹15,000' &&
                        basicPlusAllowance < restrictedWage) {

                        basicPlusAllowance += parseFloat(allowance.Amount || 0);
                        if (basicPlusAllowance > restrictedWage) {
                            basicPlusAllowance = restrictedWage;
                        }
                    }
                }
            }
        }

        return basicPlusAllowance;
    } catch (error) {
        console.error('Error in getPfSalary', error);
        throw error
    }
}

/**
 * Calculates the Provident Fund wage based on contribution rate
 * @param {string} employeeContributionRate - Employee's contribution rate setting
 * @param {string} employerContributionRate - Employer's contribution rate setting
 * @param {number} employeePfWage - Employee's PF wage
 * @param {Object} providentFundSettings - PF settings from database
 * @returns {Object} Object containing Employee_Pf_Wage and Employer_Pf_Wage
 */
function getProvidentFundWageBasedOnContributionRate(employeeContributionRate, employerContributionRate, employeePfWage, providentFundSettings) {
    try {
        let employeeWage = employeePfWage;
        let employerWage = employeePfWage;

        // Calculate employee wage based on contribution rate
        if (employeeContributionRate?.toLowerCase() === 'restrict') {
            employeeWage = Math.min(employeePfWage, providentFundSettings.Restricted_PF_Wage_Amount || 15000);
        }

        // Calculate employer wage based on contribution rate
        if (employerContributionRate?.toLowerCase() === 'restrict') {
            employerWage = Math.min(employeePfWage, providentFundSettings.Restricted_PF_Wage_Amount || 15000);
        }

        return {
            Employee_Pf_Wage: employeeWage,
            Employer_Pf_Wage: employerWage
        };
    } catch (error) {
        console.error('Error in getProvidentFundWageBasedOnContributionRate', error);
        throw error
    }
}

/**
 * Calculates current Provident Fund details including employee and employer contributions
 * @param {number} employeePfWage - Employee's PF wage
 * @param {Object} providentFundSettings - PF settings from database
 * @param {Object} providentFundDetails - Employee's PF details
 * @returns {Object} Object containing PF details including shares and charges
 */
async function calculateCurrentProvidentFundDetails(employeePfWage, providentFundSettings, providentFundDetails) {
    try {

        if (providentFundDetails?.Retirals_Type?.toLowerCase() === 'fixed') {
            return {
                Employer_Share_Amount: providentFundDetails.Employer_Share_Amount,
                Employee_Share_Amount: providentFundDetails.Employee_Share_Amount,
                Admin_Edli_Charges: {
                    Admin_Edli_Charges: 0,
                    Admin_Edli_Charges_Amount: 0
                },
                Employee_Provident_Fund_Wage: null,
                Retirals_Id: providentFundDetails.Retirals_Id,
                Employee_Retiral_Wages: null,
                Employer_Retiral_Wages: null,
                Form_Id: providentFundDetails.Form_Id,
            };
        } else {
            // Get wages based on contribution rates
            const pfWageBasedOnContributionRate = getProvidentFundWageBasedOnContributionRate(
                providentFundDetails?.PF_Employee_Contribution,
                providentFundDetails?.PF_Employer_Contribution,
                employeePfWage,
                providentFundSettings
            );

            const { Employee_Pf_Wage: employeeWage, Employer_Pf_Wage: employerWage } = pfWageBasedOnContributionRate;

            // Calculate base PF contributions
            let employeeShareAmount = (employeeWage * (providentFundDetails.Employee_Share_Percentage || 12)) / 100;
            let employerShareAmount = (employerWage * (providentFundDetails.Employer_Share_Percentage || 12)) / 100;

            // Round off the amounts
            employeeShareAmount = getRoundOffValue(formId.pfId, employeeShareAmount);
            employerShareAmount = getRoundOffValue(formId.pfId, employerShareAmount);

            // Calculate admin charge if applicable
            let adminChargeAmount = 0;
            if (providentFundDetails.Admin_Charge_Part_Of_CTC?.toLowerCase() === 'yes') {
                const adminCharge = parseFloat(providentFundSettings.Admin_Charge) || 0;
                const adminChargeMaxAmount = parseFloat(providentFundSettings.Admin_Charge_Max_Amount) || Infinity;
                adminChargeAmount = getRoundOffValue(formId.pfId, Math.min(employeeWage * (adminCharge / 100), adminChargeMaxAmount));
            }

            // Calculate EDLI charge if applicable
            let edliChargeAmount = 0;
            if (providentFundDetails.Edli_Charge_Part_Of_CTC?.toLowerCase() === 'yes') {
                const edliConfigurationEmployer = parseFloat(providentFundSettings.EDLI_Charge) || 0;
                const edliChargeMaxAmount = parseFloat(providentFundSettings.EDLI_Charge_Max_Amount) || Infinity;
                edliChargeAmount = getRoundOffValue(formId.pfId, Math.min(employeeWage * (edliConfigurationEmployer / 100), edliChargeMaxAmount));
            }

            // Prepare and return the result
            const adminEdliCharges = {
                Employee_Admin_Charge: adminChargeAmount,
                Employee_Edli_Charge: edliChargeAmount
            };
            return {
                Employer_Share_Amount: employerShareAmount,
                Employee_Share_Amount: employeeShareAmount,
                Admin_Edli_Charges: adminEdliCharges,
                Employee_Provident_Fund_Wage: employeeWage,
                Retirals_Id: providentFundDetails.Retirals_Id,
                Employee_Retiral_Wages: employeeWage,
                Employer_Retiral_Wages: employerWage,
                Form_Id: providentFundDetails.Form_Id,
            };
        }


    } catch (error) {
        console.error('Error in calculateCurrentProvidentFundDetails:', error);
        throw error;
    }
}

/**
 * Retrieves gratuity settings from database
 * @param {Object} organizationDbConnection - Database connection
 * @returns {Promise<Object>} - Gratuity settings, or null if not found
 */
async function retrieveGratuitySettings(organizationDbConnection) {
    try {
        const gratuitySettings = await organizationDbConnection(ehrTables.gratuitySettings)
            .select('Working_Days', 'Org_Salary_Days', 'Part_Of_GratuityAct')
            .first();

        if (!gratuitySettings) {
            console.log('No gratuity settings found');
            return null;
        }

        return gratuitySettings;
    } catch (error) {
        console.error('Error in retrieveGratuitySettings:', error);
        throw error;
    }
}

/**
 * Calculates gratuity amount based on basic pay and organization settings
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} basicPay - Employee's basic pay
 * @param {Object} gratuityDetails - Gratuity details
 * @param {Array} allowanceDetails - Array of allowance details
 * @returns {Promise<number>} - Calculated gratuity amount
 */
async function calculateGratuityAmount(basicPay, gratuityDetails, allowanceDetails, gratuitySettings) {
    try {
        const gratuityFormId = formId.gratuityId.toString();
        // Calculate gratuity enabled allowance
        const gratuityEnabledAllowance = getStatutorySalary(allowanceDetails, basicPay, gratuityFormId);
        // Calculate gratuity amount
        const gratuityAmount = gratuityEnabledAllowance *
            (gratuitySettings.Org_Salary_Days / gratuitySettings.Working_Days)

        return {
            'Retirals_Id': gratuityDetails.Retirals_Id,
            'Form_Id': formId.gratuityId,
            'Employee_Retiral_Wages': gratuityEnabledAllowance,
            'Employer_Retiral_Wages': gratuityEnabledAllowance,
            'Employee_Share_Amount': 0,
            'Employer_Share_Amount': getRoundOffValue(formId.gratuityId, gratuityAmount / 12),
            'Employer_Share_Percentage': null,
            'Employee_Share_Percentage': null,
            'Retiral_Type': 'Fixed'
        }

    } catch (error) {
        console.error('Error in calculateGratuityAmount:', error);
        throw error;
    }
}

/**
 * Processes NPS (National Pension System) details for an employee
 * @param {Object} organizationDbConnection - Database connection
 * @param {Array} retiralDetails - Array of retiral details
 * @param {Array} employeeAllowanceDetails - Array of employee allowance details
 * @param {number} basicPay - Employee's basic pay
 * @returns {Promise<Array>} - Array of processed NPS details
 */
async function calculateNPSDetails(employeeAllowanceDetails, basicPay, npsRetirals, npsDetails, payrollGeneralSettings) {
    try {
        let npsResults = {};

        const npsFormId = formId.npsId.toString();

        // Get NPS wages based on allowance details
        const npsWages = getStatutorySalary(employeeAllowanceDetails, basicPay, npsFormId);

        // Get payroll settings to check if NPS is slab-wise
        const isSlabWiseNps = payrollGeneralSettings && payrollGeneralSettings.Slab_Wise_NPS?.toLowerCase() === 'yes' ? true : false;
        if (isSlabWiseNps) {
            // Calculate slab-wise NPS contributions
            const npsSlabDetails = await calculateSlabWiseNps(npsDetails, npsWages);
            npsResults = npsSlabDetails;
        } else {
            if (npsRetirals?.Retirals_Type?.toLowerCase() === 'fixed') {
                npsResults = { Employee_Share_Amount: npsRetirals.Employee_Share_Amount, Employer_Share_Amount: npsRetirals.Employer_Share_Amount };
            } else {
                // Calculate percentage-based NPS contributions
                const employeeShareAmount = getRoundOffValue(formId.npsId, (npsRetirals.Employee_Share_Percentage * npsWages) / 100);
                const employerShareAmount = getRoundOffValue(formId.npsId, (npsRetirals.Employer_Share_Percentage * npsWages) / 100);
                npsResults = { Employee_Share_Amount: employeeShareAmount, Employer_Share_Amount: employerShareAmount };
            }

        }
        if (npsResults) {
            return {
                'Retirals_Id': npsRetirals.Retirals_Id,
                'Form_Id': npsRetirals.Form_Id,
                'Employee_Retiral_Wages': npsWages,
                'Employer_Retiral_Wages': npsWages,
                'Employee_Share_Amount': npsResults.Employee_Share_Amount,
                'Employer_Share_Amount': npsResults.Employer_Share_Amount,
                'Employee_Share_Percentage': npsRetirals.Employee_Share_Percentage,
                'Employer_Share_Percentage': npsRetirals.Employer_Share_Percentage,
                'Retiral_Type': npsRetirals?.Retirals_Type
            };
        }

        return null;
    } catch (error) {
        console.error('Error in calculateNPSDetails:', error);
        throw error;
    }
}

/**
 * Calculates NPS (National Pension System) contributions based on salary slabs
 * @param {Array} npsSlabDetails - Array of NPS slab details
 * @param {number} employeeNpsWage - Employee's wage for NPS calculation
 * @returns {Object} - Object containing employee and employer share amounts
 */
function calculateSlabWiseNps(npsSlabDetails, employeeNpsWage) {
    try {
        // Round off the employee's wage for NPS calculation
        employeeNpsWage = getRoundOffValue(formId.pfId, employeeNpsWage);
        let npsDetails = [];

        for (let npsSlab of npsSlabDetails) {
            // If Range_To is empty, set it to a very high number
            const rangeTo = npsSlab.Range_To || 9999999999999.99;

            if (npsSlab.Range_From <= employeeNpsWage && rangeTo >= employeeNpsWage) {
                const { Hdmf_Salary_Limit, Capped_Value, Employee_Share, Employer_Share } = npsSlab;
                let employeeShareAmount, employerShareAmount;

                if (Hdmf_Salary_Limit?.toLowerCase() === 'actual') {
                    employeeShareAmount = (employeeNpsWage * Employee_Share) / 100;
                    employerShareAmount = (employeeNpsWage * Employer_Share) / 100;
                } else {
                    employeeShareAmount = (Capped_Value * Employee_Share) / 100;
                    employerShareAmount = (Capped_Value * Employer_Share) / 100;
                }

                // Round off the amounts and return
                npsDetails = {
                    Employee_Share_Amount: getRoundOffValue(formId.npsId, employeeShareAmount),
                    Employer_Share_Amount: getRoundOffValue(formId.npsId, employerShareAmount)
                };

                return npsDetails;
            }
        }

        return npsDetails;
    } catch (error) {
        console.error('Error in calculateSlabWiseNps:', error);
        throw error;
    }
}

/**
 * Retrieves the payroll general settings from the database for a given organization.
 * @param {Object} organizationDbConnection - Database connection object for the organization's database.
 * @returns {Promise<Object>} - Promise resolving to the payroll general settings.
 * @throws Will throw an error if the database query fails.
 */
async function retrievePayrollGeneralSettings(organizationDbConnection) {
    try {
        let payrollGeneralSettings = await organizationDbConnection(ehrTables.payrollGeneralSettings)
            .select('*')
            .first()

        return payrollGeneralSettings
    } catch (err) {
        console.error('Error in retrievePayrollGeneralSettings', err);
        throw err
    }
}

/**
 * Retrieves salary configuration details for a given employee from the database.
 * 
 * @param {Object} organizationDbConnection - Knex connection to the organization's database.
 * @param {number|string} employeeId - ID of the employee whose salary configuration is to be retrieved.
 * @returns {Promise<Object>} - Promise resolving to an object containing the salary configuration details.
 * @throws Will throw an error if the database query fails.
 */
async function retrieveSalaryConfiguration(organizationDbConnection, employeeId) {
    try {
        if(employeeId){
            let salaryDetails = await organizationDbConnection(ehrTables.employeeSalaryConfiguration)
                .select('*')
                .where('Employee_Id', employeeId)
                .first()

            return salaryDetails
        }

        let salaryDetails = {
            'Eligible_For_Insurance': 1,
            'Eligible_For_Gratuity': 1,
            'Eligible_For_Pf': 1,
            'Eligible_For_Nps': 1
        }

        return salaryDetails
    } catch (err) {
        console.error('Error in retrieveSalaryConfiguration', err);
        throw err
    }
}


/**
 * Gets allowance details for a specific allowance ID with optional benefit association
 * @param {Object} organizationDbConnection - Database connection object
 * @param {Number|String} allowanceIds - The allowance ID to retrieve details for
 * @returns {Promise<Object>} - Promise resolving to allowance details
 */
async function getAllowanceDetails(organizationDbConnection, allowance) {
    try {
        let allowanceDetails = await organizationDbConnection(ehrTables.allowances + ' as A')
            .select([
                'A.Allowance_Id', 'A.Percentage', 'A.Amount', 'A.Coverage',
                'A.As_Is_Payment', 'A.FBP_Max_Declaration', 'A.Allowance_Type',
                organizationDbConnection.raw('GROUP_CONCAT(BF.Form_Id) as BenefitForms')
            ])
            .select([
                'AT.Allowance_Name', 'AT.Workflow_Id', 'AT.Allowance_Type_Id',
                'AT.Tax_Inclusion', 'AT.Formula_Based', 'AT.Period',
                'AT.Allowance_Mode', 'AT.Is_Claim_From_Reimbursement',
                'AT.Consider_For_EPF_Contribution', 'AT.Is_Flexi_Benefit_Plan',
                'AT.Perquisites_Id', 'AT.Is_Basic_Pay'
            ])
            .innerJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
            .leftJoin(ehrTables.allowanceBenefitAssociation + ' as ABA', 'ABA.Allowance_Type_Id', 'A.Allowance_Type_Id')
            .leftJoin(ehrTables.benefitForms + ' as BF', 'BF.Form_Id', 'ABA.Form_Id')
            // .where('A.Allowance_Status', 'Active')
            .groupBy('A.Allowance_Id')
            .whereIn('A.Allowance_Id', allowance.map((item) => item.Allowance_Id));

        let userAllowanceData = commonLib.func.organizeData(allowance, 'Allowance_Id')
        for (let updatedAllowance of allowanceDetails) {
            let currentRecord = ensureArray(userAllowanceData, updatedAllowance.Allowance_Id)[0]
            updatedAllowance.Amount = currentRecord.Amount
            updatedAllowance.Percentage = currentRecord.Percentage
        }

        return allowanceDetails;
    } catch (error) {
        console.error('Error in getAllowanceDetails:', error);
        throw error;
    }
}

/**
 * Calculates insurance contributions based on salary slabs
 * @param {Array} insuranceSlabDetails - Array of insurance slab details
 * @param {number} employeeInsuranceWage - Employee's wage for insurance calculation
 * @returns {Object} - Object containing employee and employer share amounts
 */
function calculateSlabWiseInsurance(insuranceSlabDetails, employeeInsuranceWage) {
    try {
        const insuranceDetails = {};

        if (!insuranceSlabDetails || !Array.isArray(insuranceSlabDetails)) {
            return insuranceDetails;
        }

        for (const insuranceSlab of insuranceSlabDetails) {
            const rangeTo = insuranceSlab.Range_To || '9999999999999.99';
            const rangeFrom = parseFloat(insuranceSlab.Range_From) || 0;
            const wage = parseFloat(employeeInsuranceWage) || 0;

            if (rangeFrom <= wage && parseFloat(rangeTo) >= wage) {
                const salaryLimit = insuranceSlab.Salary_Limit;
                const cappedValue = parseFloat(insuranceSlab.Capped_Value) || 0;
                const employeeShare = parseFloat(insuranceSlab.Employee_Share) || 0;
                const employerShare = parseFloat(insuranceSlab.Employer_Share) || 0;

                let employeeShareAmount, employerShareAmount;

                if (salaryLimit?.toLowerCase() === 'actual') {
                    employeeShareAmount = (wage * employeeShare) / 100;
                    employerShareAmount = (wage * employerShare) / 100;
                } else {
                    employeeShareAmount = (cappedValue * employeeShare) / 100;
                    employerShareAmount = (cappedValue * employerShare) / 100;
                }

                insuranceDetails.Employee_Share_Amount = getRoundOffValue(formId.pfId, employeeShareAmount);
                insuranceDetails.Employer_Share_Amount = getRoundOffValue(formId.pfId, employerShareAmount);

                return insuranceDetails;
            }
        }

        return insuranceDetails;
    } catch (error) {
        console.error('Error in calculateSlabWiseInsurance:', error);
        throw error;
    }
}

/**
 * Rounds off a value based on the specified rounding settings
 * @param {string} roundOffFor - The type of value to round off (e.g., 'EPF', 'ESI', etc.)
 * @param {number} value - The value to be rounded
 * @param {Object} [roundOffSettings=null] - Optional round off settings object
 * @param {number} [semiValue=2] - Multiplier for decimal rounding (default: 2)
 * @returns {Promise<number>} - The rounded value
 */
function getRoundOffValue(roundOffFor, value, semiValue = 2) {
    try {
        let roundOffSetting = roundOffSettings.find((item) => item.Form_Id == roundOffFor);
        if (roundOffSetting) {
            const multiplesOf = parseFloat(roundOffSetting.Multiples_Of) || 0;
            const roundOffSettingId = parseInt(roundOffSetting.Round_Off_Settings_Id) || 0;

            if (multiplesOf === 0.5) {
                switch (roundOffSettingId) {
                    case 1: // Round to nearest 0.5 or 1
                        return Math.round(value * semiValue) / semiValue;
                    case 2: // Round up to next 0.5 or 1
                        return Math.ceil(value * semiValue) / semiValue;
                    case 3: // Round down to previous 0.5 or 1
                        return Math.floor(value * semiValue) / semiValue;
                    default: // No rounding
                        return parseFloat(Number(value).toFixed(2));
                }
            } else {
                switch (roundOffSettingId) {
                    case 1: // Round to nearest integer
                        return Math.round(value);
                    case 2: // Round up to next integer
                        return Math.ceil(value);
                    case 3: // Round down to previous integer
                        return Math.floor(value);
                    default: // No rounding
                        return parseFloat(Number(value).toFixed(2));
                }
            }
        }

        return value;
    } catch (error) {
        console.error('Error in getRoundOffValue:', error);
        throw error;
    }
}

/**
 * Retrieves round off settings from the database
 * @param {string} roundOffFor - The type of value to get settings for
 * @returns {Promise<Object>} - The round off settings
 */
async function getRoundOffSettings() {
    try {
        const roundOffSettings = await organizationDbConnection(ehrTables.payrollRoundOffSettings)
            .select('Round_Off_Settings_Id', 'Multiples_Of', 'Round_Off_For', 'Form_Id')
        return roundOffSettings || [];
    } catch (error) {
        console.error('Error in getRoundOffSettings:', error);
        throw error;
    }
}

/**
 * Calculates the statutory salary including non-bonus allowances.
 * @param {Array} allowanceDetails - Array of allowance details.
 * @param {number} basicPay - Employee's basic pay.
 * @param {string} benefitFormId - Benefit form ID to check against allowance details.
 * @param {number} [adhocAllowanceAmount=0] - Additional adhoc allowance amount.
 * @returns {number} - Total statutory salary including applicable allowances.
 */
function getStatutorySalary(allowanceDetails, basicPay, benefitFormId, adhocAllowanceAmount = 0) {
    try {
        let nonBonusAllowanceDetails = allowanceDetails.filter((item) => item.Allowance_Mode?.toLowerCase() === 'non bonus');

        let basicPlusAllowance = basicPay + (parseFloat(adhocAllowanceAmount) || 0);

        if (nonBonusAllowanceDetails && nonBonusAllowanceDetails.length > 0) {
            for (const allowance of nonBonusAllowanceDetails) {
                // Check if BenefitForms exists and contains the benefitFormId
                if (allowance.BenefitForms &&
                    allowance.BenefitForms.split(',').includes(benefitFormId)) {
                    basicPlusAllowance += parseFloat(allowance.Amount || 0);
                }
            }
        }
        return getRoundOffValue(formId.salary, basicPlusAllowance);
    } catch (error) {
        console.error('Error in getStatutorySalary:', error);
        throw error;
    }
}

/**
 * Calculates allowance amount based on given basic pay and allowance details.
 * @param {number} basicPay - Basic pay amount.
 * @param {Object} allowance - Allowance details.
 * @returns {number} - Calculated allowance amount.
 */
function calculateAllowanceAmount(basicPay, allowance) {
    try {
        let allowanceAmount = 0;
        const period = {
            'Quarterly': 3,
            'HalfYearly': 6,
            'Monthly': 1,
            'Annually': 12
        };

        if (allowance.Percentage !== null && allowance.Percentage !== undefined) {
            allowanceAmount = (basicPay * (allowance.Percentage / 100)) * 1;
        } else {
            allowanceAmount = allowance.Amount / period[allowance.Period];
        }

        return getRoundOffValue(formId.salary, allowanceAmount);
    } catch (error) {
        console.error('Error in calculateAllowanceAmount:', error);
        throw error;
    }
}

/**
 * Ensures that the given result is an array.
 * @param {Object} result - The result to ensure is an array.
 * @param {string} key - The key to access in the result object.
 * @returns {Array} - The result as an array.
 */
function ensureArray(result, key) {
    return result[key] || [];
}
