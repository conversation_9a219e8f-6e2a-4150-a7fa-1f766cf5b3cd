// Common workflow functions
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ehrTables } = require('./tablealias');
const axios = require('axios');

/**
 * Get workflow process instance ID for salary revision
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} uniqueId - Unique ID (Revision_Id)
 * @returns {Promise<Array>} - Process instance data
 */
async function getWorkflowProcessInstanceId(organizationDbConnection, uniqueId) {
  try {
    return await organizationDbConnection(ehrTables.salaryRevisionDetails)
      .select(
        'Process_Instance_Id',
        'Added_On',
        'Added_By',
        organizationDbConnection.raw("CONCAT_WS(' ', EPI3.Emp_First_Name, EPI3.Emp_Middle_Name, EPI3.Emp_Last_Name) as Added_By_Name")
      )
      .leftJoin(`${ehrTables.empPersonalInfo} as EPI3`, `${ehrTables.salaryRevisionDetails}.Employee_Id`, 'EPI3.Employee_Id')
      .where('Revision_Id', uniqueId);
  } catch (err) {
    console.log('Error in getWorkflowProcessInstanceId', err);
    throw err;
  }
}

/**
 * Initiate workflow
 * @param {string} eventId - Event ID
 * @param {Object} instanceData - Instance data
 * @param {string} orgCode - Organization code
 * @param {number} formId - Form ID
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} loginEmployeeId - Login employee ID
 * @param {Object} trx - Transaction object
 * @returns {Promise<void>}
 */
async function initaiteWorkflow(eventId, instanceData, orgCode, formId, organizationDbConnection, loginEmployeeId, trx) {
  try {
    let response = await commonLib.func.initiateWorkflow(eventId, instanceData, orgCode, formId, loginEmployeeId);
    if (response && response.status === 200 && response.data?.workflowProcessInstanceId) {
      await updateWorkflowProcessInstanceId(organizationDbConnection, instanceData.id, response.data.workflowProcessInstanceId, trx);
    } else {
      console.log('Error in the initaiteWorkflow endpoint.', response);
      throw 'ATS0009';
    }
  } catch (mainError) {
    console.log('Error in initaiteWorkflow', mainError);
    throw 'ATS0009';
  }
}

async function deleteOldApprovalRecordsWithoutTrx(organizationDbConnection, processInstanceId) {
  try {
    await organizationDbConnection(ehrTables.taProcessInstanceHistory)
      .delete()
      .where("Process_Instance_Id", processInstanceId);

    await organizationDbConnection(ehrTables.taProcessInstance)
      .delete()
      .where("Process_Instance_Id", processInstanceId);

    await organizationDbConnection(ehrTables.taUserTask)
      .delete()
      .where("Process_Instance_Id", processInstanceId);

    await organizationDbConnection(ehrTables.taUserTaskHistory)
      .delete()
      .where("Process_Instance_Id", processInstanceId);

    return true;
  } catch (err) {
    console.log('Error deleting old approval records:', err);
    throw 'CHR0052';
  }
}
/**
 * Update workflow process instance ID for salary revision
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} uniqueId - Unique ID (Revision_Id)
 * @param {string} workflowProcessInstanceId - Workflow process instance ID
 * @param {Object} trx - Transaction object
 * @returns {Promise<void>}
 */
async function updateWorkflowProcessInstanceId(organizationDbConnection, uniqueId, workflowProcessInstanceId, trx) {
  try {
    return await organizationDbConnection(ehrTables.salaryRevisionDetails)
      .where('Revision_Id', uniqueId)
      .transacting(trx)
      .update({ Process_Instance_Id: workflowProcessInstanceId });
  } catch (err) {
    console.log('Error in updateWorkflowProcessInstanceId', err);
    throw err;
  }
}

/**
 * Get event ID for a form
 * @param {Object} organizationDbConnection - Database connection
 * @param {number} formId - Form ID
 * @returns {Promise<string>} - Event ID
 */
async function getEventId(organizationDbConnection, formId) {
  try {
    return (
      await organizationDbConnection(ehrTables.workflows)
        .pluck("WF.Event_Id")
        .where('WFM.Form_Id', formId)
        .innerJoin(ehrTables.workflowModule + ' as WFM', 'WFM.Workflow_Module_Id', 'WF.Workflow_Module_Id ')
        .from(ehrTables.workflows + ' as WF')
        .andWhere("WF.Default_Workflow", 1)
        .then((workflowId) => {
          console.log("workflowId", workflowId);
          let val = workflowId[0];
          if (workflowId) return val;
          else return '';
        })
        .catch((catchError) => {
          console.log("catchError", catchError);
          throw catchError;
        })
    );
  } catch (err) {
    console.log('Error in getEventId', err);
    throw err;
  }
}

/**
 * Retrieves round off settings from the database
 * @param {string} roundOffFor - The type of value to get settings for
 * @returns {Promise<Object>} - The round off settings
 */
async function getRoundOffSettings(organizationDbConnection) {
  try {
    const roundOffSettings = await organizationDbConnection(ehrTables.payrollRoundOffSettings)
      .select('Round_Off_Settings_Id', 'Multiples_Of', 'Round_Off_For', 'Form_Id')
    return roundOffSettings || [];
  } catch (error) {
    console.error('Error in getRoundOffSettings:', error);
    throw error;
  }
}

/**
 * Rounds off a value based on the specified rounding settings
 * @param {string} roundOffFor - The type of value to round off (e.g., 'EPF', 'ESI', etc.)
 * @param {number} value - The value to be rounded
 * @param {Object} [roundOffSettings=null] - Optional round off settings object
 * @param {number} [semiValue=2] - Multiplier for decimal rounding (default: 2)
 * @returns {Promise<number>} - The rounded value
 */
function getRoundOffValue(roundOffFor, value, roundOffSettings, semiValue = 2) {
  try {
    let roundOffSetting = roundOffSettings.find((item) => item.Form_Id == roundOffFor);
    if (roundOffSetting) {
      const multiplesOf = parseFloat(roundOffSetting.Multiples_Of) || 0;
      const roundOffSettingId = parseInt(roundOffSetting.Round_Off_Settings_Id) || 0;

      if (multiplesOf === 0.5) {
        switch (roundOffSettingId) {
          case 1: // Round to nearest 0.5 or 1
            return Math.round(value * semiValue) / semiValue;
          case 2: // Round up to next 0.5 or 1
            return Math.ceil(value * semiValue) / semiValue;
          case 3: // Round down to previous 0.5 or 1
            return Math.floor(value * semiValue) / semiValue;
          default: // No rounding
            return parseFloat(Number(value).toFixed(2))
        }
      } else {
        switch (roundOffSettingId) {
          case 1: // Round to nearest integer
            return Math.round(value);
          case 2: // Round up to next integer
            return Math.ceil(value);
          case 3: // Round down to previous integer
            return Math.floor(value);
          default: // No rounding
            return parseFloat(Number(value).toFixed(2));
        }
      }
    }

    return value;
  } catch (error) {
    console.error('Error in getRoundOffValue:', error);
    throw error;
  }
}

module.exports = {
  getWorkflowProcessInstanceId,
  initaiteWorkflow,
  getEventId,
  getRoundOffSettings,
  getRoundOffValue,
  deleteOldApprovalRecordsWithoutTrx
};
