# defining custom data type
scalar Date
type Query {
  getEmployerRemittanceReport(payRollMonth: String!) : getEmployerRemittanceResponse
  getPND1Report(payRollMonth: String!,serviceProviderId:Int):getPND1ReportResponse
  getMemberContributionReport(payRollMonth: String!) : getMemberContributionResponse
  getR3MonthlyContributionReport(quarterEndMonth: String!): getR3MonthlyContributionResponse
  getR5ContributionReport(assessmentYear:Int): getR5ContributionReportReponse
  getAnnualInformationReturnReport(assessmentYear:Int!): getAnnualInformationReturnReportResponse
  getCertificateOfPaymentReport(employeeId:Int!, assessmentYear:Int!): getCertificateOfPaymentReportResponse
  getIndonesiaStatutoryReport(resignationMonthYear: String!): getIndonesiaStatutoryReportResponse
  generateIndonesiaReports(month: Int, year: Int!, reportType: String!): generateIndonesiaReportsResponse
  getWithHoldTaxCertificate(employeeId:Int!, assessmentYear:Int!,serviceProviderId:Int):WithHoldTaxDetailResponse
  getPND53Report(payRollMonth: String!,serviceProviderId:Int):getPND53ReportResponse
  getPND3Report(serviceProviderId: Int, payRollMonth: String!): PND3ReportResponse!
  getPND1MonthlyReport(serviceProviderId: Int, payRollMonth: String!): PND1MonthlyReportResponse!
  calculateSalary(employeeId: Int!, retiralDetails: String!, allowanceDetails: String!, salaryDetails: String!): calculateSalaryResponse
  calculateSalaryArrears(employeeId: Int!, revisionId: Int!): calculateSalaryArrearsResponse
}

type calculateSalaryArrearsResponse{
  errorCode: String
  message: String
}

type calculateSalaryResponse{
  errorCode: String
  message: String
  employeeRetiralDetails: String
  salaryStructure: String
}

type generateIndonesiaReportsResponse{
  errorCode: String
  message: String
  headers: String
  reportData: String
}

type getR5ContributionReportReponse{
  errorCode: String
  message: String
  maillingAddress: organizationAddressData
  employerTinNo: String
  employerName: String
  paymentDetails: [SSSR5PaymentDetails]
  orgShareAmountTotal: Float
  employerShareTotal: Float
}

type SSSR5PaymentDetails {
  salaryMonth: String
  orgShareAmount: Float
  empShareAmount: Float
}

type getEmployerRemittanceResponse {
  errorCode: String
  message: String
  maillingAddress: organizationAddressData
  mobileNo: String
  employerTinNo: String
  employerName: String
  employerType: String
  reportType: String
  dateReceived: Date
  employeeDetails:[employeeDatas]
  employerShareTotal: Float
  personalShareTotal: Float
}
type getPND53ReportResponse{
 errorCode:String 
 message:String
 employeeDetails:String
}
type PND3ReportResponse {
  reportType: String
  pnd3Details: String
  errorCode: String
  message: String
}

type EmployeeAddressPND53 {
  apartmentName: String
  streetName: String
  city: String
  state: String
  country: String
  pincode: String
}
type getPND1ReportResponse {
 errorCode:String 
 message:String,
 data:ReportData 
}
type MailingAddress {
  tan: String
  taxAgent: String
  street1: String
  street2: String
  cityName: String
  stateName: String
  countryName: String
  pincode: String
  mobileNo: String
}

type SalariesAndWages {
  name: String
  employeeCount: Int
  totalAmountOfIncome: Float
  amountWithHoldingTax: Float
  total: Float
}
type EmployeeDetails {
  employeeCount: Int
  taxableSalaryCount: Float
}
type TaxDetails{
  Receipt_No: String
  paymentDate: String
  bankName: String
  branchName: String
  documentNo: String
  amountPaid: Float
  totalAmountOfIncome: Float
  totalAmountOfIncomeInWords: String
}
type ReportData {
  maillingAddress: MailingAddress
  taxDetails: TaxDetails
  reportType: String
  employeeDetails: [EmployeeDetails]
  employeeCount: Int
  summaryHoldingTaxDetails:summaryHoldingTaxDetails
  errorCode: String
  message: String
}
type summaryHoldingTaxDetails{
  generalSalariesAndWages: SalariesAndWages
  approvedSalariesWithholdingTax: SalariesAndWages
  terminationPaymentIncome: SalariesAndWages
  residentIncomeSection40_2: SalariesAndWages
  nonResidentIncomeSection40_2: SalariesAndWages
}
type organizationAddressData {
  street1: String
  street2: String
  cityName: String
  stateName: String
  countryName: String
  pincode: String
  phone: String
}

type employeeDatas {
  identificationNumber: String
  firstName: String
  middleName: String
  lastName: String
  dateOfBirth: Date
  gender: String
  employerShare: Float
  personalShare: Float
  separationDate: Date
  orgShareAmount1: Float
  orgShareAmount2: Float
  orgShareAmount3: Float
  empShareAmount1: Float
  empShareAmount2: Float
  empShareAmount3: Float
  rtnNumber: String
}

type getMemberContributionResponse {
  errorCode: String
  message: String
  maillingAddress: organizationAddressData
  mobileNo: String
  employerName: String
  dateReceived: Date
  employeeDetails:[employeeDatas]
  employerShareTotal: Float
  personalShareTotal: Float
}

type getR3MonthlyContributionResponse {
  errorCode: String
  message: String
  maillingAddress: organizationAddressData
  mobileNo: String
  employerName: String
  typeOfEmployer: String
  employeeDetails:[employeeDatas]
  totalOrgShareAmount1: Float
  totalOrgShareAmount2: Float
  totalOrgShareAmount3: Float
  totalEmpShareAmount1: Float
  totalEmpShareAmount2: Float
  totalEmpShareAmount3: Float
}

type getAnnualInformationReturnReportResponse {
  errorCode: String
  message: String
  maillingAddress: organizationAddressData
  identificationNumber: String
  agentName: String
  paymentDetails: [paymentData]
  amenededReturn: String
  numberOfSheetAttach: String
  withholdingAgent: String
  yearEndAdjustment: String
}

type paymentData {
  paymentDate: Date
  bankName: String
  documentNo: String
  totalAmount: Float
}

type getCertificateOfPaymentReportResponse {
   errorCode: String
   message: String
   assessmentYear: Int
   taxPayerIdentificationNo: Int
   firstName: String
   lastName: String
   middleName: String
   dateofBirth: Date
   mobileNo: String
   mobileCountryCode: String
   permenantAddress: employeeAddressData
   currentAddress: employeeAddressData
   employerIdentificationNo: String
   employerName: String
   maillingAddress: organizationAddressData
   qualifiedDependent: String
   totalExemptions: Int
   premiumPaidHealth: Int
   taxDue: Int
   bPreviousEmployer: Int
   salariesAndCompensation: Int
   holidayPayMWE: String
   overtimePayMWE: String
   hazardPayMWE: String
   monthPay: String
   deMinimisBenefits: String
   basicSalary: String
   representation: String
   transportaion: String
   costLivingAllowance: String
   fixedHousingAllowance: String
   commission: String
   profitSharing: String
   feesIncludingDirector: String
   taxableMonthPay: String
   hazardPay: String
   overtimePay: String
   presentEmployerGrossIncome: String
   totalNonTaxable: String
   presentEmployerTaxableIncome: String
   previousEmployerTaxableIncome: String
   otherBenefitsGrossTaxableIncome: String
}

type employeeAddressData{
  apartmentName: String
  streetName: String
  city: String
  state: String
  country: String
  pincode: String
}

type getIndonesiaStatutoryReportResponse {
  errorCode: String
  message: String
  headers: String
  reportData: String
}

type MailingCompanyAddress {
  tan: String
  taxAgent: String
  street1: String
  street2: String
  cityName: String
  stateName: String
  countryName: String
  pincode: String
  mobileNo: String
}

type Address {
  apartmentName: String
  streetName: String
  city: String
  state: String
  country: String
  pincode: String
  mobileNo: String
  mobileNoCode: String
}

type taxHoldingEmployeeDetails {
  tan: String
  personalIdentificationNo: String
  name: String
  address: Address
}

type TaxWithHoldingDetails {
  salaryWagePensionSection40_1: TaxDetail
  commissionsSection40_2: TaxDetail
  royaltiesSection40_3: TaxDetail
  interestSection40_4_a: TaxDetail
  incomeSubjectToWithholdingSection3Tredecim: TaxDetail
  others: TaxDetail
}

type TaxDetail {
  amountPaid: Float
  taxWithHeld: Float
}

type WithHoldTaxDetailResponse {
  mailingCompanyAddress: MailingCompanyAddress
  employeeDetails: taxHoldingEmployeeDetails
  reportType: String
  taxWithHoldingDetails: TaxWithHoldingDetails
  errorCode: String
  message: String
}

type PND1MonthlyReportResponse {
  errorCode: String 
  message: String,
  data: PND1MonthlyReportData 
}

type PND1MonthlyReportData {
  personalIdentificationNo: String
  taxpayerIdentificationNo: String
  typeofIncome1: String
  typeofIncome2: String
  typeofIncome3: String
  typeofIncome4: String
  typeofIncome5: String
  branchNo: String
  paymentDate: Date
  employeeDetails: [PND1EmployeePaymentData]
  position: String
  payerResponsibleName: String
  fillingDate: Date
  totalPaidAmount: Float
  totalAmountOfTaxWithHeld: Float
}

type PND1EmployeePaymentData{
  paidAmount: Float
  amountOfTaxWithHeld: Float
  empPersonalIdentificationNo: String
  empTaxIdentificationNo: String
  surname: String
  employeeName: String
  conditions: String
}


schema {
  query: Query
}
