# defining custom data type
scalar Date

type Query {
  listSalaryTemplateDetails(
    formId: Int!
    templateId: Int
    id: Int
    employeeId: Int
    isViewMode: Boolean
    isDropdown: Boolean
    includeHistoricalRecords: Boolean
  ): listSalaryTemplateResponse!
  listSalaryComponents: listSalaryComponentsResponse!
  getRetiralComponents: getRetiralComponentsResponse!
  getSalaryDetails(employeeId: Int!): getSalaryDetailsResponse!
  retrieveSalaryConfiguration(
    employeeId: Int!
  ): retrieveSalaryConfigurationResponse!
  getEffectiveDate(employeeId: Int!, action: String!): getEffectiveDateResponse!
}
type Mutation {
  deleteSalaryTemplate(templateId: Int!): deleteSalaryTemplateResponse!
  updateSalaryConfiguration(
    employeeId: Int!
    eligibleForOvertime: Int
    eligibleForPf: Int
    eligibleForPension: Int!
    exemptEDLI: Int
    UAN: String
    PfPolicyNo: String
    eligibleForESI: Int
    ESINumber: String
    eligibleForInsurance: Int
    eligibleForNps: Int
    NpsNumber: String
    eligibleForGratuity: Int
    eligibleForPT: Int
    eligibleForVpf: Int!
    vpfType: String
    vpfEmployeeShare: Float
    vpfEmployeeShareAmount: Float
    bondRecoveryApplicable: String
    minimumMonthsToBeServed: Int
    bondValue: Float
  ): updateSalaryConfigurationResponse!
  addSalaryTemplate(
    isEditForm: Int!
    templateId: Int
    templateName: String!
    annualCTC: String!
    basicPayType: String!
    percentage: String
    amount: String
    description: String
    allowance: [allowanceInput]
    retirals: [templateRetiralsInput]
  ): addSalaryTemplateResponse!
  updateTemplateStatus(
    templateId: Int!
    templateStatus: String!
  ): updateTemplateStatusResponse!
  addSalaryDetails(
    isEditForm: Int!
    employeeId: Int!
    templateId: Int!
    basicPay: String!
    effectiveFrom: String!
    effectiveTo: String
    annualCTC: String!
    annualGrossSalary: String!
    monthlyGrossSalary: String!
    allowance: [allowanceInput]
    retirals: [retiralsInput]
  ): addSalaryDetailsResponse!

  addUpdateSalaryDetails(
    # Common parameters
    formId: Int!
    isEditMode: Boolean
    id: Int
    annualCTC: String!
    allowance: [allowanceInput]

    # Salary Template parameters (formId: 206)
    templateName: String
    description: String
    templateStatus: String

    # Salary Form and Revision parameters (formId: 207, 360)
    employeeId: Int
    templateId: Int
    effectiveFrom: String
    effectiveTo: String
    annualGrossSalary: String
    monthlyGrossSalary: String
    salaryEffectiveMonth: String
    retirals: [retiralsInput]

    # Salary Revision specific parameters (formId: 360)
    payoutMonth: String
    revisionType: String
    revisionStatus: String
    reviseCtcByPercentage: String
    previousCtc: String
  ): addSalaryDetailsResponse!

  deleteSalaryRecord(
    id: Int
    formId: Int!
    employeeId: Int
  ): deleteSalaryRecordResponse!
}

input allowanceInput {
  allowanceId: Int!
  allowanceType: String!
  allowanceWages: String
  amount: String
  percentage: String
}

input templateRetiralsInput {
  formId: String!
  retiralsId: String
  retiralsType: String!
  employeeRetiralWages: String
  employerRetiralWages: String
  employerSharePercentage: String
  employerShareAmount: String
  pfEmployerContribution: String
  employerStatutoryLimit: String
  eligibleForEPS: Int!
  adminCharge: String
  edliCharge: String
}

input retiralsInput {
  formId: String!
  retiralsId: String
  retiralsType: String!
  employeeRetiralWages: String
  employerRetiralWages: String
  employeeSharePercentage: String
  employerSharePercentage: String
  employeeShareAmount: String
  employerShareAmount: String
  pfEmployeeContribution: String
  pfEmployerContribution: String
  employeeStatutoryLimit: String
  employerStatutoryLimit: String
  eligibleForEPS: Int!
  adminCharge: String
  edliCharge: String
}

type listSalaryTemplateResponse {
  errorCode: String
  message: String
  currencySymbol: String
  templateDetails: String
}

type deleteSalaryTemplateResponse {
  errorCode: String
  message: String
}

type listSalaryComponentsResponse {
  errorCode: String
  message: String
  salaryComponents: String
}

type getRetiralComponentsResponse {
  errorCode: String
  message: String
  retirals: [retiralsList]
}

type retiralsList {
  formId: Int
  formName: String
}

type updateSalaryConfigurationResponse {
  errorCode: String
  message: String
}

type addSalaryTemplateResponse {
  errorCode: String
  message: String
}

type getSalaryDetailsResponse {
  errorCode: String
  message: String
  salaryDetails: String
  currencySymbol: String
}

type updateTemplateStatusResponse {
  errorCode: String
  message: String
}

type retrieveSalaryConfigurationResponse {
  errorCode: String
  message: String
  salaryConfigurationDetails: [salaryConfigurationDetails]
  retiralsDetails: retiralsDetails
}

type salaryConfigurationDetails {
  Eligible_For_Overtime: Int
  Eligible_For_Pf: Int
  Eligible_For_Pension: Int
  Exempt_EDLI: Int
  UAN: String
  Pf_PolicyNo: String
  Eligible_For_ESI: Int
  ESI_Number: String
  Eligible_For_Insurance: Int
  Eligible_For_Nps: Int
  Nps_Number: String
  Eligible_For_Gratuity: Int
  Eligible_For_PT: Int
  Eligible_For_Vpf: Int
  Vpf_Type: String
  Vpf_Employee_Share: Float
  Vpf_Employee_Share_Amount: Float
  Bond_Recovery_Applicable: String
  Minimum_Months_To_Be_Served: Int
  Bond_Value: Float
  Added_On: String
  Added_By: String
  Updated_On: String
  Updated_By: String
}

type addSalaryDetailsResponse {
  errorCode: String
  message: String
}

type deleteSalaryRecordResponse {
  errorCode: String
  message: String
}

type retiralsDetails {
  providentFundExist: Int
  professionalTaxExist: Int
}

type getEffectiveDateResponse {
  errorCode: String
  message: String
  effectiveDate: String
  effectiveDateRange: [String]
}

type revisionPayslipComponent {
  Allowance_Id: Int
  Allowance_Name: String
  Amount: String
}

type retiralsPayslipComponent {
  Form_Id: String
  Retirals_Id: String
  Employee_Share_Amount: String
  Employer_Share_Amount: String
  Insurance_Name: String
  Insurance_Type: String
}

schema {
  query: Query
  mutation: Mutation
}
