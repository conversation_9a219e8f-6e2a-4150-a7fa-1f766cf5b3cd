// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { ApolloError,UserInputError } = require('apollo-server-lambda');
const moment = require('moment-timezone');
const validation = require('../formvalidations/validation');
const {
  getWorkflowProcessInstanceId,
  initaiteWorkflow,
  getEventId,
  deleteOldApprovalRecordsWithoutTrx
} = require('../common/commonfunctions');
const { calculateSalary } = require('../roresolvers/salary/calculateSalary');
const { calculateSalaryArrears } = require('../roresolvers/salary/calculateSalaryArrears');

/**
 * Main resolver function to add/update salary details
 * @param {Object} args - Arguments passed to the resolver
 * @param {Object} context - Context object containing user info
 * @returns {Object} - Response with operation status
 */
const addUpdateSalaryDetails = async (_, args, context) => {
  let organizationDbConnection;
  let validationError = {};
  try {
    console.log('Inside addUpdateSalaryDetails function');

    // Get user info from context
    const {
      logInEmpId: loginEmployeeId,
      orgCode,
      userIp
    } = context;
    const formId = args.formId || 207; // Default to salary form
    const isEditMode = args.isEditMode || false;

    // Initialize database connection
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Get table configuration based on formId
    const tableConfig = getTableConfig(formId);

    // Check access rights based on operation type
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      null,
      '',
      'UI',
      false,
      formId
    );

    // Verify user has appropriate access rights
    if (isEditMode) {
      // Check edit access
      if (Object.keys(checkRights).length === 0 || checkRights.Role_Update !== 1) {
        throw '_DB0102'; // No edit access
      }
    } else {
      // Check add access
      if (Object.keys(checkRights).length === 0 || checkRights.Role_Add !== 1) {
        throw '_DB0101'; // No add access
      }
    }

    // Validate inputs based on form type
    if (formId === 206) {
      // Validate salary template inputs
      if (!validation.multilingualNameNumericValidation(args.templateName)) {
        validationError['IVE0609'] = commonLib.func.getError('', 'IVE0609').message1;
      }
      if (args.templateName && args.templateName.length < 2 || args.templateName.length > 30) {
        validationError['IVE0608'] = commonLib.func.getError('', 'IVE0608').message1;
      }
      if(args.description && args.description.length > 0 && !validation.multilingualNameNumericValidation(args.description)){
        validationError['IVE0610'] = commonLib.func.getError('', 'IVE0610').message1;
      }
      if(args.description && (args.description.length <2 ||args.description.length > 600)) {
        validationError['IVE0611'] = commonLib.func.getError('', 'IVE0611').message1;
      }
    }
    if (args.formId === 360 && args.payoutMonth) {
      let employeeSalaryType = await commonLib.payroll.getEmployeeSalaryType(organizationDbConnection, args.employeeId);
      let employeeMaxPayslipMonth = await commonLib.func.maxPayslipMonth(organizationDbConnection, args.employeeId, employeeSalaryType);
     let {Resignation_Date} = await getEmployeeDetails(organizationDbConnection, args.employeeId);
     if (Resignation_Date) {
      if (moment(Resignation_Date).isSameOrBefore(moment(employeeMaxPayslipMonth, 'M,YYYY').startOf('month'))) {
        throw 'IVE0619';
      }
    }
     if (employeeMaxPayslipMonth) {
        const payoutMoment = moment(args.payoutMonth, 'M,YYYY').startOf('month');
        const [year, month] = employeeMaxPayslipMonth.split('-');
        const payslipMoment = moment(`${year}-${month}`, 'YYYY-MM').startOf('month');
        if (payoutMoment.isSameOrBefore(payslipMoment)) {
          validationError['IVE0612'] = commonLib.func.getError('', 'IVE0612').message1;
        }
      }
    }
    if (args.formId === 207 || args.formId === 360) {
      const payConfig = await getEmployeePayConfigDetails(organizationDbConnection, args.employeeId);
      const {Date_Of_Join} = await getEmployeeDetails(organizationDbConnection, args.employeeId);

      if (args.salaryEffectiveMonth && Date_Of_Join) {
        const [inputMonth, inputYear] = args.salaryEffectiveMonth.split(',');
        const effectiveMonthDate = moment(`${inputYear}-${inputMonth.padStart(2, '0')}-01`);
        const doj = moment(Date_Of_Join);
        // const minAllowedDate = moment().startOf('month').subtract(6, 'months');
      
        // 1. Check if Date of Join is after salary effective month
        if (doj.isAfter(effectiveMonthDate)) {
          throw 'IVE0617';
        }
        // // 2. Check if salary effective month is older than 6 months
        // else if (effectiveMonthDate.isBefore(minAllowedDate)) {
        //   throw 'IVE0618';
        // }
      }
      
      if (payConfig) {
      let {
        Eligible_For_Gratuity,
        Eligible_For_Insurance,
        Eligible_For_Teacher_Provident_Fund,
        Eligible_For_Special_Provident_Fund,
        Eligible_For_Nps
      } = payConfig;
    
      for (let i = 0; i < args.retirals.length; i++) {
        const retiral = args.retirals[i];
    
        if (retiral.formId === 52) {
          if (
            Eligible_For_Teacher_Provident_Fund?.toLowerCase() !== 'yes' &&
            Eligible_For_Special_Provident_Fund?.toLowerCase() !== 'yes' &&
            Eligible_For_Nps?.toLowerCase() !== 'yes'
          ) {
            throw 'PST0019';
          }
        } else if (retiral.formId === 58) {
          if (Eligible_For_Insurance?.toLowerCase() !== 'yes') {
            throw 'PST0020';
          }
        } else if (retiral.formId === 110) {
          if (Eligible_For_Gratuity?.toLowerCase() !== 'yes') {
            throw 'PST0021';
          }
        } else if (retiral.formId === 126) {
          if (Eligible_For_Nps?.toLowerCase() !== 'yes') {
            throw 'PST0022';
          }
        }
      }
    }
    }

    // Validation for formId 360 (Salary Revision) - Check for duplicate Applied status and salary effective month validation
    if (args.formId === 360 && args.employeeId) {
      await validateSalaryRevision(organizationDbConnection, args, isEditMode);
    }
    if (
      args.formId === 360 &&
      (!args.salaryEffectiveMonth || !moment(args.salaryEffectiveMonth, 'M,YYYY').isSame(moment(args.payoutMonth, 'M,YYYY'), 'month'))
    ){
      const monthList = getMonthsBetweenDates(
        moment(args.salaryEffectiveMonth, 'M,YYYY').format('YYYY-M'),
        moment(args.payoutMonth, 'M,YYYY').format('YYYY-M')
    );

    const { salaryPayslips, resignationDate } = await getSalaryPayslipWithResignation(
        organizationDbConnection,
        args.employeeId,
        monthList
    );
    if(!salaryPayslips?.length){
        throw 'PST0024';
    }
  }

    if (Object.keys(validationError).length > 0) {
        throw 'IVE0000';
    }

    // Current UTC timestamp
    const currentTimestamp = moment().utc().format('YYYY-MM-DD HH:mm:ss');

    if((args.formId === 360 || args.formId === 207 )&& args.salaryEffectiveMonth && args.salaryEffectiveMonth.length > 0){
      const [month, year] = args.salaryEffectiveMonth.split(',')
      let { Last_SalaryDate } = await commonLib.func.getSalaryDay(
      orgCode,
          organizationDbConnection,
          month,
          year
      );
      if (moment(Last_SalaryDate, 'YYYY-MM-DD', true).isValid()) {
          args.salaryEffectiveMonth = moment(Last_SalaryDate).format('M,YYYY');   
      }
    }
       // Call calculateSalary function for salary details (formId 207)
       if (parseInt(args.formId) === 207) {
        try {
          const salaryCalculationResult = await callCalculateSalary(
            organizationDbConnection,
            args,
            args.id,
            orgCode
          );
          if (salaryCalculationResult.errorCode && salaryCalculationResult.errorCode.length > 0) {
            throw salaryCalculationResult.errorCode;
          }
          const isValid = validateRetiralsMatch(salaryCalculationResult, args);
          if (!isValid) {
            console.log("Retirals mismatch")
            throw 'PST0013';
          }
        } catch (calculationError) {
          console.log('Error in salary calculation:', calculationError);
          throw calculationError;
        }
      }
      let insertId;
    if (isEditMode) {
      // For edit mode, update existing record
      await updateSalaryDetails(
        organizationDbConnection,
        args,
        loginEmployeeId,
        tableConfig,
        currentTimestamp,
        orgCode,
        userIp
      );
      insertId = args.id;

      return {
        errorCode: "",
        message: `${tableConfig.entityName} updated successfully`
      };
    } else {
      insertId = await insertSalaryDetails(
        organizationDbConnection,
        args,
        loginEmployeeId,
        tableConfig,
        currentTimestamp,
        orgCode,
        userIp
      );

      if (
        args.formId === 360 &&
        (!args.salaryEffectiveMonth || !moment(args.salaryEffectiveMonth, 'M,YYYY').isSame(moment(args.payoutMonth, 'M,YYYY'), 'month'))
      ){
        try{
        args.revisionId=insertId;
        let context={
          orgdb:organizationDbConnection
        }
        await calculateSalaryArrears(
          null,
          args,
          context,
          null
        );
        }catch (error) {
          // Cleanup: Delete all records added during this operation (only for add scenario, not update)
          if (!args.isEditMode && insertId) {
            try {
                await organizationDbConnection(ehrTables.salaryRevisionAllowance)
                  .where('Revision_Id', insertId)
                  .delete();
                await organizationDbConnection(ehrTables.salaryRevisionRetirals)
                  .where('Revision_Id', insertId)
                  .delete();
                await organizationDbConnection(ehrTables.salaryRevisionDetails)
                  .where('Revision_Id', insertId)
                  .delete();

                  const responseObject = await getWorkflowProcessInstanceId(
                    organizationDbConnection,
                    insertId
                  );
                  if (responseObject && responseObject[0]?.Process_Instance_Id) {
                    await deleteOldApprovalRecordsWithoutTrx(
                      organizationDbConnection,
                      responseObject[0].Process_Instance_Id
                    );
                  }
                  throw error;
            } catch(error) {
              console.log('Error while calling calculateSalaryArrears function', error);
              throw 'PST0025';
            }
          }
        
          throw 'PST0025';
        }
        
      }

      return {
        errorCode: "",
        message: `${tableConfig.entityName} added successfully`
      };
    }
  } catch (error) {
    console.log('Error in addUpdateSalaryDetails function main catch block', error);

    let errorCode = error?.code === 'ER_DUP_ENTRY' ? 'PST0023' : error || 'PST0013';
    let errResult = commonLib.func.getError(errorCode, 'PST0013');

    if (error === 'IVE0000') {
      errResult = commonLib.func.getError('', 'IVE0000');
      throw new UserInputError(errResult.message, { validationError: validationError });
    }
    else{
 
    if(errorCode === 'PST0025'){
      errResult.message = 'Oops! Something went wrong while adding salary revision details, please contact the platform administrator.';
    }
    if (errorCode === 'PST0023') {
      if (args.formId === 207) {
        errResult.message = 'Duplicate Employee Salary: The employee salary details already exist. Please choose a different employee.';
      } else if (args.formId === 360) {
        errResult.message = 'Duplicate Salary Revision: The salary revision already exists. Please choose a different revision period.';
      } else if (args.formId === 206) {
        errResult.message = 'Duplicate Salary Template: The salary template already exists. Please choose a different template name.';
      }
    }
    if(errorCode === 'PST0024'){
      errResult.message = 'No salary payslips found for the given employee ID and month list';
    }

    throw new ApolloError(errResult.message, errResult.code);
  }
  }
   finally {
    if (organizationDbConnection) organizationDbConnection.destroy();
  }
 
};

/**
 * Get table configuration based on form ID
 * @param {number} formId - Form ID to determine table configuration
 * @returns {Object} - Configuration object with table details
 */
function getTableConfig(formId) {
  switch (parseInt(formId)) {
    case 206: // Salary Template
      return {
        entityName: 'Salary template',
        mainTable: ehrTables.salaryTemplate,
        allowanceTable: ehrTables.templateAllowanceComponents,
        retiralsTable: ehrTables.templateRetiralComponents,
        primaryKey: 'Template_Id',
        foreignKey: 'Template_Id',
        statusField: 'Template_Status'
      };
    case 207: // Salary Form
      return {
        entityName: 'Salary details',
        mainTable: ehrTables.employeeSalaryDetails,
        allowanceTable: ehrTables.employeeSalaryAllowance,
        retiralsTable: ehrTables.employeeSalaryRetirals,
        primaryKey: 'Employee_Id',
        foreignKey: 'Employee_Id'
      };
    case 360: // Salary Revision
      return {
        entityName: 'Salary revision',
        mainTable: ehrTables.salaryRevisionDetails,
        allowanceTable: ehrTables.salaryRevisionAllowance,
        retiralsTable: ehrTables.salaryRevisionRetirals,
        primaryKey: 'Revision_Id',
        foreignKey: 'Revision_Id'
      };
    default:
      throw new Error(`Invalid formId: ${formId}`);
  }
}

/**
 * Insert new salary details
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments from resolver
 * @param {string} loginEmployeeId - ID of logged in employee
 * @param {Object} tableConfig - Table configuration
 * @param {string} currentTimestamp - Current UTC timestamp
 * @param {string} orgCode - Organization code
 * @param {string} userIp - User IP address
 * @returns {Promise<string>} - Success message
 */
async function insertSalaryDetails(organizationDbConnection, args, loginEmployeeId, tableConfig, currentTimestamp, orgCode, userIp) {
  try {
    return await organizationDbConnection.transaction(async (trx) => {
      // Prepare main record data based on form type
      let mainTableData = {};

      // Common fields for all forms
      const commonFields = {
        Added_On: currentTimestamp,
        Added_By: loginEmployeeId
      };
      switch (parseInt(args.formId)) {
        case 206: // Salary Template
          if (!args.templateName) {
            throw new Error('Template name is required for salary template');
          }
          mainTableData = {
            Template_Name: args.templateName,
            Annual_Ctc: args.annualCTC,
            Description: args.description || '',
            Template_Status: args.templateStatus || 'Active',
            ...commonFields
          };
          break;

        case 207: // Salary Form
          mainTableData = {
            Employee_Id: args.employeeId,
            Template_Id: args.templateId,
            Effective_From: args.effectiveFrom,
            Annual_Ctc: args.annualCTC,
            Annual_Gross_Salary: args.annualGrossSalary,
            Monthly_Gross_Salary: args.monthlyGrossSalary,
            Salary_Effective_Month: args.salaryEffectiveMonth,
            ...commonFields
          };
          break;

        case 360: // Salary Revision
          mainTableData = {
            Employee_Id: args.employeeId,
            Template_Id: args.templateId,
            Salary_Effective_Month: args.salaryEffectiveMonth,
            Effective_From: args.effectiveFrom,
            Revision_Type: args.revisionType,
            Revision_Status: args.revisionStatus,
            Previous_Ctc: args.previousCtc,
            Revise_Ctc_By_Percentage: args.reviseCtcByPercentage || null,
            Payout_Month: args.payoutMonth || null,
            Annual_Ctc: args.annualCTC,
            Annual_Gross_Salary: args.annualGrossSalary,
            Monthly_Gross_Salary: args.monthlyGrossSalary,
            ...commonFields
          };
          break;

        default:
          throw new Error(`Invalid formId: ${args.formId}`);
      }

      // Insert main record
      const [insertId] = await organizationDbConnection(tableConfig.mainTable)
        .insert(mainTableData)
        .transacting(trx);

      if (parseInt(args.formId) === 360) {
        const Basic_Pay= await getBasicPay(organizationDbConnection, args.employeeId);
        const instanceData = {
          formId: Number(args.formId ?? 360),
          id: Number(insertId ?? 0),
          annualCTC: String(args.annualCTC ?? ""),
          employeeId: Number(args.employeeId ?? 0),
          Employee_Id: Number(args.employeeId ?? 0),
          templateId: Number(args.templateId ?? 0),
          salaryEffectiveMonth: String(args.salaryEffectiveMonth ?? ""),
          effectiveFrom: String(args.effectiveFrom ?? ""),
          annualGrossSalary: String(args.annualGrossSalary ?? ""),
          Revise_Ctc_By_Percentage:  String(args.reviseCtcByPercentage ?? ""),
          basicPay: String(Basic_Pay && Basic_Pay.Basic_Pay? Basic_Pay.Basic_Pay : ""),
          effectiveTo: String(args.effectiveTo ?? ""),
          monthlyGrossSalary: String(args.monthlyGrossSalary ?? ""),
          payoutMonth: String(args.payoutMonth ?? ""),
          revisionType: String(args.revisionType ?? ''),
          revisionStatus: String(args.revisionStatus ?? ''),
          previousCtc: String(args.previousCtc ?? ''),
          Added_On: String(currentTimestamp),
          Added_By: Number(loginEmployeeId)
        };
        // Get employee name for workflow
        let employeeNameObject = await getEmployeeName(organizationDbConnection, loginEmployeeId);
        if (!employeeNameObject) {
          console.log("login employee details not found", loginEmployeeId);
          throw 'ETR0003';
        }

        instanceData.Added_By_Name = employeeNameObject.employeeName;

        // Get event ID and initiate workflow
        const eventId = await getEventId(organizationDbConnection, args.formId);
        if (eventId && (!args.revisionStatus || args.revisionStatus.toLowerCase() !== 'rejected')) {
          await initaiteWorkflow(
            eventId,
            instanceData,
            orgCode,
            args.formId,
            organizationDbConnection,
            loginEmployeeId,
            trx
          );
        }
      }
      // Process allowances
      if (args.allowance && args.allowance.length > 0) {
        // Validate allowances before processing
        for (let item of args.allowance) {
          if (item.allowanceType.toLowerCase() === 'percentage' && (!item.allowanceWages || item.allowanceWages.trim() === '')) {
            throw 'IVE0615'
          }
        }

        const allowanceData = args.allowance.map(item => ({
          [`${tableConfig.foreignKey}`]: args.formId === 207 ? args.employeeId : insertId,
          Allowance_Id: item.allowanceId,
          Allowance_Type: item.allowanceType,
          Allowance_Wages: item.allowanceWages || null,
          Percentage: item.percentage || null,
          Amount: item.amount || null
        }));

        await organizationDbConnection(tableConfig.allowanceTable)
          .insert(allowanceData)
          .transacting(trx);
      }

      // Process retirals - only for salary form and revision
      if (args.retirals && args.retirals.length > 0) {
        for(let item of args.retirals){
          if(item.retiralsType.toLowerCase() === 'percentage' && (!item.employeeRetiralWages || item.employeeRetiralWages.trim() === '') && (!item.employerRetiralWages || item.employerRetiralWages.trim() === '')){
            throw 'IVE0616'
          }
        }
        const retiralsData = args.retirals.map(item => ({
          [`${tableConfig.foreignKey}`]: args.formId === 207 ? args.employeeId : insertId,
          Form_Id: item.formId,
          Retirals_Id: item.retiralsId,
          Retirals_Type: item.retiralsType,
          Employee_Retiral_Wages: item.employeeRetiralWages || null,
          Employer_Retiral_Wages: item.employerRetiralWages || null,
          Employee_Share_Percentage: item.employeeSharePercentage || null,
          Employer_Share_Percentage: item.employerSharePercentage || null,
          Employee_Share_Amount: item.employeeShareAmount || null,
          Employer_Share_Amount: item.employerShareAmount || null,
          PF_Employee_Contribution: item.pfEmployeeContribution || null,
          PF_Employer_Contribution: item.pfEmployerContribution || null,
          Employee_Statutory_Limit: item.employeeStatutoryLimit || null,
          Employer_Statutory_Limit: item.employerStatutoryLimit || null,
          Eligible_For_EPS: item.eligibleForEPS || 0,
          Admin_Charge: item.adminCharge || null,
          EDLI_Charge: item.edliCharge || null
        }));

        await organizationDbConnection(tableConfig.retiralsTable)
          .insert(retiralsData)
          .transacting(trx);
      }

      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `Salary revision for employee ${args.employeeId} ${args.isEditMode ? 'updated' : 'added'} successfully`
      });

      return insertId;
    });
  } catch (error) {
    console.log('Error in insertSalaryDetails function', error);
    throw error;
  }
}

/**
 * Update existing salary details
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments from resolver
 * @param {string} loginEmployeeId - ID of logged in employee
 * @param {Object} tableConfig - Table configuration
 * @param {string} currentTimestamp - Current UTC timestamp
 * @param {string} orgCode - Organization code
 * @param {string} userIp - User IP address
 * @returns {Promise<string>} - Success message
 */
async function updateSalaryDetails(organizationDbConnection, args, loginEmployeeId, tableConfig, currentTimestamp, orgCode, userIp) {
  try {
    return await organizationDbConnection.transaction(async (trx) => {
      // Get the record to update
      const recordToUpdate = await organizationDbConnection(tableConfig.mainTable)
        .select('*')
        .where(tableConfig.primaryKey, args.formId === 207 ? args.employeeId : args.id)
        .first()
        .transacting(trx);

      if (!recordToUpdate) {
        throw 'PST0014'; // Record not found
      }

      // Common update fields
      const commonFields = {
        Updated_On: currentTimestamp,
        Updated_By: loginEmployeeId
      };

      // Prepare main record data based on form type
      let mainTableData = {};

      switch (parseInt(args.formId)) {
        case 206: // Salary Template
          // For template, we need to ensure Template_Name exists
          if (args.templateName === '' && !recordToUpdate.Template_Name) {
            throw new Error('Template name is required for salary template');
          }
          mainTableData = {
            Template_Name: args.templateName || recordToUpdate.Template_Name,
            Annual_Ctc: args.annualCTC || recordToUpdate.Annual_Ctc,
            Description: args.description || recordToUpdate.Description,
            Template_Status: args.templateStatus || recordToUpdate.Template_Status,
            ...commonFields
          };
          break;
        case 207: // Salary Form
          // For salary form, check if we need to update Effective_To on previous record
          if (args.effectiveFrom && args.effectiveFrom !== recordToUpdate.Effective_From) {
            // This is a new effective date, so we should handle it appropriately
            mainTableData = {
              Employee_Id: args.employeeId || recordToUpdate.Employee_Id,
              Template_Id: args.templateId || recordToUpdate.Template_Id,
              Effective_From: args.effectiveFrom,
              Annual_Ctc: args.annualCTC || recordToUpdate.Annual_Ctc,
              Annual_Gross_Salary: args.annualGrossSalary || recordToUpdate.Annual_Gross_Salary,
              Monthly_Gross_Salary: args.monthlyGrossSalary || recordToUpdate.Monthly_Gross_Salary,
              Salary_Effective_Month: args.salaryEffectiveMonth || recordToUpdate.Salary_Effective_Month,
              ...commonFields
            };
          } else {
            // Regular update without changing effective date
            mainTableData = {
              Employee_Id: args.employeeId || recordToUpdate.Employee_Id,
              Template_Id: args.templateId || recordToUpdate.Template_Id,
              Effective_From: recordToUpdate.Effective_From,
              Annual_Ctc: args.annualCTC || recordToUpdate.Annual_Ctc,
              Annual_Gross_Salary: args.annualGrossSalary || recordToUpdate.Annual_Gross_Salary,
              Monthly_Gross_Salary: args.monthlyGrossSalary || recordToUpdate.Monthly_Gross_Salary,
              Salary_Effective_Month: args.salaryEffectiveMonth || recordToUpdate.Salary_Effective_Month,
              ...commonFields
            };
          }
          break;

        case 360: // Salary Revision
          mainTableData = {
            Employee_Id: args.employeeId || recordToUpdate.Employee_Id,
            Template_Id: args.templateId || recordToUpdate.Template_Id,
            Effective_From: args.effectiveFrom || recordToUpdate.Effective_From,
            Salary_Effective_Month: args.salaryEffectiveMonth || recordToUpdate.Salary_Effective_Month,
            Payout_Month: args.payoutMonth || recordToUpdate.Payout_Month,
            Revision_Type: args.revisionType || recordToUpdate.Revision_Type,
            Revision_Status: args.revisionStatus || recordToUpdate.Revision_Status,
            Previous_Ctc: args.previousCtc || recordToUpdate.Previous_Ctc,
            Revise_Ctc_By_Percentage: args.reviseCtcByPercentage || recordToUpdate.Revise_Ctc_By_Percentage,
            Annual_Ctc: args.annualCTC || recordToUpdate.Annual_Ctc,
            Annual_Gross_Salary: args.annualGrossSalary || recordToUpdate.Annual_Gross_Salary,
            Monthly_Gross_Salary: args.monthlyGrossSalary || recordToUpdate.Monthly_Gross_Salary,
            ...commonFields
          };
          break;

        default:
          throw new Error(`Invalid formId: ${args.formId}`);
      }

      // Update main record
      await organizationDbConnection(tableConfig.mainTable)
        .update(mainTableData)
        .where(tableConfig.primaryKey, args.formId === 207 ? args.employeeId : args.id)
        .transacting(trx);

      // Handle workflow for formId 360 updates
      if (parseInt(args.formId) === 360) {
        // Get workflow process instance ID
        const responseObject = await getWorkflowProcessInstanceId(
          organizationDbConnection,
          args.id
        );

        // Prepare instance data for workflow
        const instanceData = {
          formId: Number(args.formId ?? 360),
          id: Number(args.id ?? 0),
          annualCTC: String(args.annualCTC ?? recordToUpdate.Annual_Ctc ?? ""),
          employeeId: Number(args.employeeId ?? recordToUpdate.Employee_Id ?? 0),
          Employee_Id: Number(args.employeeId ?? recordToUpdate.Employee_Id ?? 0),
          templateId: Number(args.templateId ?? recordToUpdate.Template_Id ?? 0),
          effectiveFrom: String(args.effectiveFrom ?? recordToUpdate.Effective_From ?? ""),
          salaryEffectiveMonth: String(args.salaryEffectiveMonth ?? recordToUpdate.Salary_Effective_Month ?? ""),
          Revise_Ctc_By_Percentage:  String(args.reviseCtcByPercentage ?? ""),
          annualGrossSalary: String(args.annualGrossSalary ?? recordToUpdate.Annual_Gross_Salary ?? ""),
          monthlyGrossSalary: String(args.monthlyGrossSalary ?? recordToUpdate.Monthly_Gross_Salary ?? ""),
          payoutMonth: String(args.payoutMonth ?? recordToUpdate.Payout_Month ?? ""),
          revisionType: String(args.revisionType ?? recordToUpdate.Revision_Type ?? ""),
          revisionStatus: String(args.revisionStatus ?? recordToUpdate.Revision_Status ?? ""),
          previousCtc: String(args.previousCtc ?? recordToUpdate.Previous_Ctc ?? ""),
          reviseCtcByPercentage: String(args.reviseCtcByPercentage ?? recordToUpdate.Revise_Ctc_By_Percentage ?? ""),
          Updated_On: String(currentTimestamp),
          Updated_By: Number(loginEmployeeId)
        };
        // Get employee name for workflow
        let employeeNameObject = await getEmployeeName(organizationDbConnection, loginEmployeeId);
        if (!employeeNameObject) {
          console.log("login employee details not found", loginEmployeeId);
          throw 'ETR0003';
        }

        instanceData.Updated_By = employeeNameObject.employeeName;
        if (responseObject[0]) {
          instanceData.Added_On = responseObject[0].Added_On;
          instanceData.Added_By_Name = responseObject[0].Added_By_Name;
          instanceData.Added_By = Number(loginEmployeeId);
        }

        // Get event ID and initiate workflow
        const eventId = await getEventId(organizationDbConnection, args.formId);
        if (eventId && args.revisionStatus && args.revisionStatus.toLowerCase() !== 'rejected') {
          await initaiteWorkflow(
            eventId,
            instanceData,
            orgCode,
            args.formId,
            organizationDbConnection,
            loginEmployeeId,
            trx
          );

          // Delete old approval records if process instance exists
          if (responseObject && responseObject[0]?.Process_Instance_Id) {
            await commonLib.func.deleteOldApprovalRecords(
              organizationDbConnection,
              responseObject[0].Process_Instance_Id,
              trx
            );
          }
        }
      }

      // Handle allowances - delete and reinsert
      if (args.allowance && args.allowance.length > 0) {
        // Validate allowances before processing
        for (let item of args.allowance) {
          if (item.percentage && (!item.allowanceWages || item.allowanceWages.trim() === '')) {
            throw new Error('Allowance wages is required when percentage is provided');
          }
        }

        // Delete existing allowances
        const deleteValue = args.formId === 207 ? (args.employeeId || recordToUpdate.Employee_Id) : args.id;
        await organizationDbConnection(tableConfig.allowanceTable)
          .where(tableConfig.foreignKey, deleteValue)
          .delete()
          .transacting(trx);

        // Insert new allowances
        for(let item of args.allowance){
          if(item.allowanceType.toLowerCase() === 'percentage' && (!item.allowanceWages || item.allowanceWages.trim() === '')){
            throw 'IVE0615'
          }
        }
        const allowanceData = args.allowance.map(item => ({
          [`${tableConfig.foreignKey}`]: args.formId === 207 ? (args.employeeId || recordToUpdate.Employee_Id) : args.id,
          Allowance_Id: item.allowanceId,
          Allowance_Type: item.allowanceType,
          Allowance_Wages: item.allowanceWages || null,
          Percentage: item.percentage || null,
          Amount: item.amount || null
        }));

        await organizationDbConnection(tableConfig.allowanceTable)
          .insert(allowanceData)
          .transacting(trx);
      }

      // Handle retirals - delete and reinsert (only for salary form and revision)
      if (args.retirals && args.retirals.length > 0) {
        // Delete existing retirals
        const retiralsDeleteValue = args.formId === 207 ? (args.employeeId || recordToUpdate.Employee_Id) : args.id;
        await organizationDbConnection(tableConfig.retiralsTable)
          .where(tableConfig.foreignKey, retiralsDeleteValue)
          .delete()
          .transacting(trx);
        // Insert new retirals
        for(let item of args.retirals){
          if(item.retiralsType.toLowerCase() === 'percentage' && (!item.employeeRetiralWages || item.employeeRetiralWages.trim() === '') && (!item.employerRetiralWages || item.employerRetiralWages.trim() === '')){
            throw 'IVE0616'
          }
        }
        const retiralsData = args.retirals.map(item => ({
          [`${tableConfig.foreignKey}`]: args.formId === 207 ? (args.employeeId || recordToUpdate.Employee_Id) : args.id,
          Form_Id: item.formId,
          Retirals_Id: item.retiralsId,
          Retirals_Type: item.retiralsType,
          Employee_Retiral_Wages: item.employeeRetiralWages || null,
          Employer_Retiral_Wages: item.employerRetiralWages || null,
          Employee_Share_Percentage: item.employeeSharePercentage || null,
          Employer_Share_Percentage: item.employerSharePercentage || null,
          Employee_Share_Amount: item.employeeShareAmount || null,
          Employer_Share_Amount: item.employerShareAmount || null,
          PF_Employee_Contribution: item.pfEmployeeContribution || null,
          PF_Employer_Contribution: item.pfEmployerContribution || null,
          Employee_Statutory_Limit: item.employeeStatutoryLimit || null,
          Employer_Statutory_Limit: item.employerStatutoryLimit || null,
          Eligible_For_EPS: item.eligibleForEPS || 0,
          Admin_Charge: item.adminCharge || null,
          EDLI_Charge: item.edliCharge || null
        }));

        await organizationDbConnection(tableConfig.retiralsTable)
          .insert(retiralsData)
          .transacting(trx);
      }

      return 'success';
    });
  } catch (error) {
    console.log('Error in updateSalaryDetails function', error);
    throw error;
  }
}

/**
 * Get employee name by employee ID
 * @param {Object} organizationDbConnection - Database connection
 * @param {string} employeeId - Employee ID
 * @returns {Promise<Object>} - Employee name object
 */
async function getEmployeeName(organizationDbConnection, employeeId) {
  try {
    return await organizationDbConnection(ehrTables.empPersonalInfo + " as EP")
      .select( organizationDbConnection.raw(`
        CONCAT_WS(" ", EP.Emp_First_Name, EP.Emp_Middle_Name, EP.Emp_Last_Name) as employeeName
      `),)
      .where('Employee_Id', employeeId)
      .first();
  } catch (error) {
    console.log('Error in getEmployeeName function', error);
    throw error;
  }
}

/**
 * Helper function to call calculateSalary with proper data formatting
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments from resolver
 * @param {number} recordId - Record ID (insertId or args.id)
 * @param {string} orgCode - Organization code
 * @returns {Promise<Object>} - Calculation result
 */
async function callCalculateSalary(organizationDbConnection, args, recordId, orgCode) {
  try {
    // Get employee status from emp_job table
    const employeeJobData = await organizationDbConnection(ehrTables.empJob)
      .select('Emp_Status')
      .where('Employee_Id', args.employeeId)
      .first();

    const employeeStatus = employeeJobData ? employeeJobData.Emp_Status : 'Active';

    // Prepare the salaryDetails object exactly as expected
    const salaryDetails = {
      Employee_Salary_Id: recordId,
      Employee_Id: args.employeeId,
      Template_Id: args.templateId,
      Annual_Ctc: parseFloat(args.annualCTC),
      Effective_From: args.effectiveFrom,
      Annual_Gross_Salary: args.annualGrossSalary ? parseFloat(args.annualGrossSalary) : null,
      Monthly_Gross_Salary: args.monthlyGrossSalary ? parseFloat(args.monthlyGrossSalary) : null,
      Salary_Effective_Month: args.salaryEffectiveMonth || null,
      ESI_Contribution_End_Date: null,
      Status: employeeStatus
    };

    // Format allowance details exactly as expected
    const allowanceDetails = args.allowance ? args.allowance.map(item => ({
      Employee_Salary_Id: recordId,
      Allowance_Id: item.allowanceId,
      Allowance_Type: item.allowanceType,
      Allowance_Wages: item.allowanceWages ? parseFloat(item.allowanceWages) : null,
      Percentage: item.percentage ? parseFloat(item.percentage) : null,
      Amount: item.amount ? parseFloat(item.amount) : null
    })) : [];

    // Format retiral details exactly as expected
    const retiralDetails = args.retirals ? args.retirals.map(item => ({
      Employee_Salary_Id: recordId,
      Form_Id: parseInt(item.formId),
      Retirals_Id: parseInt(item.retiralsId),
      Retirals_Type: item.retiralsType,
      Employee_Retiral_Wages: item.employeeRetiralWages ? parseFloat(item.employeeRetiralWages) : null,
      Employer_Retiral_Wages: item.employerRetiralWages ? parseFloat(item.employerRetiralWages) : null,
      Employee_Share_Percentage: item.employeeSharePercentage ? parseFloat(item.employeeSharePercentage) : null,
      Employer_Share_Percentage: item.employerSharePercentage ? parseFloat(item.employerSharePercentage) : null,
      Employee_Share_Amount: item.employeeShareAmount ? parseFloat(item.employeeShareAmount) : null,
      Employer_Share_Amount: item.employerShareAmount ? parseFloat(item.employerShareAmount) : null,
      PF_Employee_Contribution: item.pfEmployeeContribution || null,
      PF_Employer_Contribution: item.pfEmployerContribution || null,
      Employee_Statutory_Limit: item.employeeStatutoryLimit ? parseFloat(item.employeeStatutoryLimit) : null,
      Employer_Statutory_Limit: item.employerStatutoryLimit ? parseFloat(item.employerStatutoryLimit) : null,
      Eligible_For_EPS: item.eligibleForEPS || 0,
      Admin_Charge: item.adminCharge ? parseFloat(item.adminCharge) : null,
      EDLI_Charge: item.edliCharge ? parseFloat(item.edliCharge) : null
    })) : [];

    // Create context object for calculateSalary
    const context = {
      Org_Code: orgCode,
      orgdb: organizationDbConnection
    };
    // Call calculateSalary function with exact format as your example
    const result = await calculateSalary(
      null, // parent
      {
        employeeId: args.employeeId,
        retiralDetails: JSON.stringify(retiralDetails),
        allowanceDetails: JSON.stringify(allowanceDetails),
        salaryDetails: JSON.stringify(salaryDetails)
      },
      context,
      null // info
    );
    if(result.errorCode){
      throw result.errorCode;
    }
    return result;
  } catch (error) {
    console.log('Error in callCalculateSalary function', error);
    throw error;
  }
}

// Export resolvers
const resolvers = {
  Mutation: {
    addUpdateSalaryDetails
  }
};


async function getEmployeePayConfigDetails(organizationDbConnection, employeeId){
  try {
    return await organizationDbConnection(ehrTables.employeeSalaryConfiguration)
    .select('*')
    .where('Employee_Id', employeeId)
    .first();
  } catch (error) {
    console.log('Error in getEmployeePayConfigDetails function', error);
    throw error;
  }
}

async function getBasicPay(organizationDbConnection, employeeId){
  try {
    return await organizationDbConnection(ehrTables.employeeSalaryAllowance + ' as ESL')
      .select('ESL.Amount as Basic_Pay')
      .innerJoin(ehrTables.allowances + ' as A', 'ESL.Allowance_Id', 'A.Allowance_Id')
      .innerJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
      .where('AT.Is_Basic_Pay', 'Yes')
      .where('ESL.Employee_Id', employeeId)
      .first();
  } catch (error) {
    console.log('Error in getBasicPay function', error);
    throw error;
  }
}

async function getEmployeeDetails(organizationDbConnection, employeeId){
  try {
    return await organizationDbConnection(ehrTables.empJob + ' as EJ')
      .select('EJ.*', 'R.Resignation_Date')
      .leftJoin(ehrTables.empResignation+' as R' , function () {
        this.on('EJ.Employee_Id', '=', 'R.Employee_Id')
          .onIn('R.Approval_Status', organizationDbConnection.raw('(?,?)', ['Applied', 'Approved']))
      })
      .where('EJ.Employee_Id', employeeId)
      .first();
  } catch (error) {
    console.log('Error in getEmployeeDetails function', error);
    throw error;
  }
}

function validateRetiralsMatch(responseData, args) {
  let apiRetirals;
  if (!responseData || !responseData.employeeRetiralDetails) return false;
  if (typeof responseData.employeeRetiralDetails === 'string') {
    try {
      apiRetirals = JSON.parse(responseData.employeeRetiralDetails).employeeSalaryRetirals;
    } catch {
      return false;
    }
  } else {
    apiRetirals = responseData.employeeRetiralDetails.employeeSalaryRetirals;
  }
  if (!Array.isArray(apiRetirals) || !Array.isArray(args?.retirals)) return false;
  if (apiRetirals.length !== args.retirals.length) return false;
  const toNum = v => parseInt(v, 10);
  return args.retirals.every(inputItem =>{
    return apiRetirals.some(apiItem =>{
      return toNum(inputItem.formId) === apiItem.Form_Id &&
      toNum(inputItem.retiralsId) === apiItem.Retirals_Id
    }
    )
  }
  );
}



/**
 * Validate salary revision for duplicate Applied status and salary effective month
 * @param {Object} organizationDbConnection - Database connection
 * @param {Object} args - Arguments from resolver
 * @param {boolean} isEditMode - Whether this is an edit operation
 */
async function validateSalaryRevision(organizationDbConnection, args, isEditMode) {
  try {
    // Check for existing Applied status records
    let query = organizationDbConnection(ehrTables.salaryRevisionDetails)
      .select('*')
      .where('Employee_Id', args.employeeId)
      .where('Revision_Status', 'Applied');

    // For edit mode, exclude the current record being edited
    if (isEditMode && args.id) {
      query = query.whereNot('Revision_Id', args.id);
    }

    const existingAppliedRecord = await query.first();
    if (existingAppliedRecord) {
      throw 'PST0023';
    }

    // Check for approved records with salary effective month validation
    if (args.payoutMonth) {
      let approvedQuery = organizationDbConnection(ehrTables.salaryRevisionDetails)
        .select('*')
        .where('Employee_Id', args.employeeId)
        .where('Revision_Status', 'Complete');

      // For edit mode, exclude the current record being edited
      if (isEditMode && args.id) {
        approvedQuery = approvedQuery.whereNot('Revision_Id', args.id);
      }

      const existingApprovedRecord = await approvedQuery.first();

      if (existingApprovedRecord && existingApprovedRecord.Salary_Effective_Month) {
        const existingApprovedSalaryEffectiveMoment = moment(existingApprovedRecord.Salary_Effective_Month, 'M,YYYY');
        const newPayoutMoment = moment(args.payoutMonth, 'M,YYYY');
        // Check if existing approved record's salary effective month is greater than new record's payout month
        if (existingApprovedSalaryEffectiveMoment.isSameOrAfter(newPayoutMoment, 'month')) {
          throw 'PST0023';
        }
      }
    }

    return true;
  } catch (error) {
    console.log('Error in validateSalaryRevision function:', error);
    throw error;
  }
}
async function getSalaryPayslipWithResignation(db, employeeId, monthList) {
  try {
      if (monthList.length === 0) return [];

      const [salaryPayslips, resignationData] = await Promise.all([
          db(ehrTables.salaryPayslip + ' as SP')
              .select('SP.*', 'EJ.Date_Of_Join')
              .innerJoin(ehrTables.empJob + ' as EJ', 'SP.Employee_Id', 'EJ.Employee_Id')
              .where('SP.Employee_Id', employeeId)
              .whereIn('SP.Salary_Month', monthList),

          db(ehrTables.empResignation)
              .select('Resignation_Date')
              .where('Approval_Status', 'Approved')
              .where('Employee_Id', employeeId)
              .first()
      ]);

      if (resignationData) {
          //Check if the resignation comes between the monthList
          const resignationMonth = moment(resignationData.Resignation_Date).format('M,YYYY');
          const resignationIndex = monthList.indexOf(resignationMonth);
          if (resignationIndex !== -1) {
              monthList.splice(resignationIndex, 1);
          }
      }

      return {
          salaryPayslips,
          resignationDate: resignationData?.Resignation_Date
      };
  } catch (err) {
      console.error('Error in getSalaryPayslipWithResignation function:', err);
      throw err;
  }
}

function getMonthsBetweenDates(startMonthYear, endMonthYear) {
  try {
      if (!startMonthYear || !endMonthYear) {
          throw new Error('Start and end month-year are required');
      }

      const parseMonthYear = (monthYear) => {
          const [year, month] = monthYear.split('-').map(num => parseInt(num.trim()));
          if (isNaN(month) || isNaN(year) || month < 1 || month > 12) {
              throw new Error(`Invalid month-year format: ${monthYear}`);
          }
          return { month, year };
      };

      const start = parseMonthYear(startMonthYear);
      const end = parseMonthYear(endMonthYear);

      const months = [];
      let current = { ...start };

      while (
          current.year < end.year ||
          (current.year === end.year && current.month <= end.month)
      ) {
          months.push(`${current.month},${current.year}`);

          current.month++;
          if (current.month > 12) {
              current.month = 1;
              current.year++;
          }
      }

      return months;
  } catch (err) {
      console.error('Error in getMonthsBetweenDates:', err);
      throw err;
  }
}

exports.resolvers = resolvers;
