// require resolver files
const listSalaryTemplateDetails = require('./resolvers/listSalaryTemplateDetails');
const deleteSalaryTemplate = require('./resolvers/deleteSalaryTemplate');
const addUpdateSalaryDetails = require('./resolvers/addUpdateSalaryDetails');
const listSalaryComponents = require('./resolvers/listSalaryComponents');
const updateSalaryConfiguration = require('./resolvers/updateSalaryConfiguration');
//need to handle with existing list modules with some changes
const getRetiralComponents = require('./resolvers/getRetiralComponents');
const getSalaryDetails = require('./resolvers/getSalaryDetails');
const updateTemplateStatus = require('./resolvers/updateTemplateStatus');
const retrieveSalaryConfiguration = require('./resolvers/retrieveSalaryConfiguration');
const getEffectiveDate = require('./resolvers/getEffectiveDate');
const deleteSalaryRecord = require('./resolvers/deleteSalaryRecord');

// Define resolver
const resolvers = {
    Query: Object.assign({},
        listSalaryTemplateDetails.resolvers.Query,
        getRetiralComponents.resolvers.Query,
        listSalaryComponents.resolvers.Query,
        getSalaryDetails.resolvers.Query,
        retrieveSalaryConfiguration.resolvers.Query,
        { getEffectiveDate: getEffectiveDate.getEffectiveDate }
    ),
    Mutation: Object.assign({},
        deleteSalaryTemplate.resolvers.Mutation,
        updateTemplateStatus.resolvers.Mutation,
        updateSalaryConfiguration.resolvers.Mutation,
        addUpdateSalaryDetails.resolvers.Mutation,
        deleteSalaryRecord.resolvers.Mutation
    )
}
exports.resolvers = resolvers;