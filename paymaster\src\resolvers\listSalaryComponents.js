// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const { ApolloError } = require('apollo-server-lambda');
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const commonFunctions = require('../common/salaryTemplateCommonFunctions');
const { formId } = require('../common/appconstants');

// resolver definition
const resolvers = {
    Query: {
        // function to list salary template components
        listSalaryComponents: async (parent, args, context, info) => {
            let organizationDbConnection;
            try{
                console.log('Inside listSalaryComponents function');
                const loggedInEmpId = context.logInEmpId;

                // get the organization database connection
                organizationDbConnection = knex(context.connection.OrganizationDb);

                // check access right based on employeeid
                const checkRights = await commonLib.func.checkEmployeeAccessRights(
                    organizationDbConnection,
                    loggedInEmpId,
                    null,
                    '',
                    'UI',
                    false,
                    206 // formId for salary template
                );

                if (Object.keys(checkRights).length === 0 || checkRights.Role_View !== 1) {
                    throw '_DB0100';
                }

                // get allowance components
                const allowanceDetails = await getAllowanceComponents(organizationDbConnection);
                const allowanceLength = Object.keys(allowanceDetails).length;
                const fixedAllowanceArray = (allowanceLength > 0) ? allowanceDetails.fixedAllowanceArray : [];
                const flexiBenefitPlanArray = (allowanceLength > 0) ? allowanceDetails.flexiBenefitPlanArray : [];
                const reimbursementArray = (allowanceLength > 0) ? allowanceDetails.reimbursementArray : [];
                const bonusArray = (allowanceLength > 0) ? allowanceDetails.bonusArray : [];
                const allowanceArray = (allowanceLength > 0) ? allowanceDetails.allowanceArray : [];
                const basicPayArray = (allowanceLength > 0) ? allowanceDetails.basicPayArray : [];

                // get retiral components
                const retiralDetails = await getRetiralComponents(organizationDbConnection);

                // form output json response
                const salaryComponents = {
                    'allowances': {
                        fixedAllowanceArray,
                        flexiBenefitPlanArray,
                        reimbursementArray,
                        bonusArray,
                        allowanceArray,
                        basicPayArray
                    },
                    'retirals': retiralDetails
                };

                // return response
                return {
                    errorCode: '',
                    message: 'Salary components listed successfully.',
                    salaryComponents: JSON.stringify(salaryComponents)
                };
            }
            catch (mainCatchError){
                console.log('Error in listSalaryComponents function main catch block', mainCatchError);
                const errResult = commonLib.func.getError(mainCatchError, 'PST0004');
                throw new ApolloError(errResult.message, errResult.code);
            }
            finally{
                // destroy database connection
                if (organizationDbConnection) organizationDbConnection.destroy();
            }
        }
    }
};

// function to get allowance components
async function getAllowanceComponents(organizationDbConnection) {
    // variable declarations
    let fixedAllowanceArray = [];
    let flexiBenefitPlanArray = [];
    let reimbursementArray = [];
    let allowanceArray = [];
    let bonusArray = [];
    let basicPayArray = [];

    try {
        return await organizationDbConnection.transaction(async (trx) => {
            // get the allowance components which are in active status and organization coverage
            const getDetails = await organizationDbConnection(ehrTables.allowances + ' as A')
                .select(
                    'A.*',
                    'A.Amount as Org_Amount',
                    'AT.*',
                    organizationDbConnection.raw('GROUP_CONCAT(BA.Form_Id) as Form_Id'),
                    'A.FBP_Max_Declaration'
                )
                .innerJoin(ehrTables.allowanceType + ' as AT', 'A.Allowance_Type_Id', 'AT.Allowance_Type_Id')
                .leftJoin('allowance_type_benefit_association as BA', 'AT.Allowance_Type_Id', 'BA.Allowance_Type_Id')
                .leftJoin(ehrTables.ehrForms + ' as EF', 'EF.Form_Id', 'BA.Form_Id')
                .where('A.Coverage', 'ORG')
                // .andWhere('A.Allowance_Status', 'Active')
                .andWhere('AT.AllowanceType_Status', 'Active')
                .groupBy('A.Allowance_Id', 'AT.Allowance_Type_Id')
                .orderBy([
                    { column: 'AT.Allowance_Sequence', order: 'asc' },
                    { column: 'AT.Allowance_Name', order: 'asc' }
                ])
                .transacting(trx);

            if (getDetails.length > 0) {
                for (let allowanceData of getDetails) {
                    const response = await commonFunctions.benefitAssociation(allowanceData);
                    const mappedData = mapAllowanceData(response);

                    // Categorize based on allowance type properties
                    if (allowanceData['Formula_Based'] && allowanceData['Formula_Based'].toLowerCase() === 'yes') {
                        fixedAllowanceArray.push(mappedData);
                    } else if (allowanceData['Is_Claim_From_Reimbursement'] && allowanceData['Is_Claim_From_Reimbursement'].toLowerCase() === 'yes') {
                        reimbursementArray.push(mappedData);
                    } else if (allowanceData['Is_Flexi_Benefit_Plan'] && allowanceData['Is_Flexi_Benefit_Plan'].toLowerCase() === 'yes') {
                        flexiBenefitPlanArray.push(mappedData);
                    } else if (allowanceData['Allowance_Mode'] && allowanceData['Allowance_Mode'].toLowerCase() === 'bonus') {
                        bonusArray.push(mappedData);
                    } else if (allowanceData['Allowance_Mode'] && allowanceData['Allowance_Mode'].toLowerCase() === 'non bonus' &&
                              allowanceData['Is_Basic_Pay'] && allowanceData['Is_Basic_Pay'].toLowerCase() === 'no') {
                        allowanceArray.push(mappedData);
                    } else if (allowanceData['Is_Basic_Pay'] && allowanceData['Is_Basic_Pay'].toLowerCase() === 'yes') {
                        basicPayArray.push(mappedData);
                    } else {
                        // Default to allowance array for any unmatched cases
                        allowanceArray.push(mappedData);
                    }
                }
                return {
                    fixedAllowanceArray,
                    flexiBenefitPlanArray,
                    reimbursementArray,
                    bonusArray,
                    allowanceArray,
                    basicPayArray
                };
            } else {
                return {};
            }
        });
    } catch (error) {
        console.log('Error in getAllowanceComponents function main catch block', error);
        return {};
    }
}

/**
 * Map allowance data to consistent format (same as listSalaryTemplateDetails.js)
 * @param {Object} response - Raw allowance data
 * @returns {Object} - Formatted allowance data
 */
function mapAllowanceData(response) {
    return {
        'Allowance_Id': response['Allowance_Id'],
        'Allowance_Name': response['Allowance_Name'],
        'Allowance_Type': response['Allowance_Type'],
        'Is_Basic_Pay': response['Is_Basic_Pay'],
        'Period': response['Period'],
        'Amount': response['Amount'],
        'AllowanceWages': response['Allowance_Wages'],
        'Percentage': response['Percentage'],
        'orgAmount': response['Org_Amount'],
        'Form_Id': response['Form_Id'],
        'Form_Name': response['Form_Name'],
        'pfMapped': response['pfMapped'],
        'variableInsuranceMapped': response['variableInsuranceMapped'],
        'fixedInsuranceMapped': response['fixedInsuranceMapped'],
        'gratuityMapped': response['gratuityMapped'],
        'npsMapped': response['npsMapped']
    };
}

// function to get retiral components
async function getRetiralComponents(organizationDbConnection) {
    try {
        // Get payroll general settings for slab-wise flags
        const payrollSettings = await organizationDbConnection('payroll_general_settings')
            .select('Slab_Wise_NPS', 'Slab_Wise_PF')
            .first();

        // Get insurance components from insurance_configuration table
        const insuranceDetails = await organizationDbConnection(ehrTables.insuranceConfiguration)
            .select('*')
            .where('InsuranceType_Status', 'Active');

        // Categorize insurance types
        const fixedInsurance = insuranceDetails.filter(item =>
            item.Insurance_Type?.toLowerCase() === 'fixed'
        );

        const variableInsurance = insuranceDetails.filter(item =>
            item.Insurance_Type?.toLowerCase() === 'variable' &&
            item.Employee_State_Insurance?.toLowerCase() !== 'yes'
        );

        const esiDetails = insuranceDetails.filter(item =>
            item.Employee_State_Insurance?.toLowerCase() === 'yes'
        );
        const slabWiseInsurance = insuranceDetails.filter(item =>
            item.Slab_Wise_Insurance?.toLowerCase() === 'yes'
        );
        // Get NPS details
        const npsDetails = await getNPSDetails(organizationDbConnection, payrollSettings);

        // Get Gratuity details
        const gratuityDetails = await getGratuityDetails(organizationDbConnection);

        // Get PF details
        const pfDetails = await getPFDetails(organizationDbConnection, payrollSettings);

        return {
            fixedInsurance: fixedInsurance.map(item => ({
                ...item,
                Form_Id: formId.insurance,
                Retiral_Type: item.Insurance_Type==='Fixed' ? 'Amount' : 'Percentage',
                Period: item.Payment_Frequency
            })),
            variableInsurance: variableInsurance.map(item => ({
                ...item,
                Form_Id: formId.insurance,
                Retiral_Type: item.Insurance_Type==='Fixed' ? 'Amount' : 'Percentage',
                Period: item.Payment_Frequency
            })),
            esiDetails: esiDetails.map(item => ({
                ...item,
                Form_Id: formId.insurance,
                Retiral_Type: item.Insurance_Type==='Fixed' ? 'Amount' : 'Percentage',
                Period: item.Payment_Frequency
            })),
            slabWiseInsurance: slabWiseInsurance.map(item => ({
                Form_Id: formId.insurance,
                Retiral_Type: item.Insurance_Type==='Fixed' ? 'Amount' : 'Percentage',
                ...item,
                Period: item.Payment_Frequency
            })),
            npsDetails,
            gratuityDetails,
            pfDetails
        };
    } catch (error) {
        console.log('Error in getRetiralComponents function:', error);
        return {};
    }
}

// function to get NPS details
async function getNPSDetails(organizationDbConnection, payrollSettings) {
    try {
        const npsResult = {
            Slab_Wise_NPS: payrollSettings?.Slab_Wise_NPS || 'No',
            Period: 'Monthly',
            Form_Id: formId.npsId,
            Retiral_Type: 'Percentage'
        };

        // If Slab_Wise_NPS is No, get details from nps_configuration
        if (payrollSettings?.Slab_Wise_NPS?.toLowerCase() === 'no') {
            const npsConfig = await organizationDbConnection('nps_configuration')
                .select('Employee_Share', 'Employer_Share')
                .first();

            if (npsConfig) {
                npsResult.Employee_Share = npsConfig.Employee_Share;
                npsResult.Employer_Share = npsConfig.Employer_Share;
            }
        }

        return [npsResult];
    } catch (error) {
        console.log('Error in getNPSDetails function:', error);
        return [];
    }
}

// function to get gratuity details
async function getGratuityDetails(organizationDbConnection) {
    try {
        const gratuityDetails = await organizationDbConnection(ehrTables.gratuitySettings)
            .select('Working_Days', 'Org_Salary_Days', 'Part_Of_GratuityAct');

        if (gratuityDetails.length > 0) {
            return gratuityDetails.map(item => ({
                ...item,
                Period: 'Annually',
                Form_Id: formId.gratuityId,
                Retiral_Type: 'Percentage'
            }));
        } else {
            return [];
        }
    } catch (error) {
        console.log('Error in getGratuityDetails function:', error);
        return [];
    }
}

// function to get PF details
async function getPFDetails(organizationDbConnection, payrollSettings) {
    try {
        const pfResult = {
            Slab_Wise_PF: payrollSettings?.Slab_Wise_PF || 'No',
            Period: 'Monthly',
            Form_Id: formId.pfId,
            Retiral_Type: 'Percentage'
        };

        // If Slab_Wise_PF is No, get details from provident_fund_settings and provident_fund
        if (payrollSettings?.Slab_Wise_PF?.toLowerCase() === 'no') {
            const pfSettings = await organizationDbConnection('provident_fund_settings')
                .select('Restricted_PF_Wage_Amount', 'Employee_Share', 'Employer_Share')
                .first();

            const pfConfig = await organizationDbConnection('provident_fund')
                .select('Employee_Contribution_Rate', 'Employer_Contribution_Rate')
                .first();

            if (pfSettings) {
                pfResult.Restricted_PF_Wage_Amount = pfSettings.Restricted_PF_Wage_Amount;
                pfResult.Employee_Share = pfSettings.Employee_Share;
                pfResult.Employer_Share = pfSettings.Employer_Share;
            }

            if (pfConfig) {
                pfResult.Employee_Contribution_Rate = pfConfig.Employee_Contribution_Rate;
                pfResult.Employer_Contribution_Rate = pfConfig.Employer_Contribution_Rate;
            }
        }

        return [pfResult];
    } catch (error) {
        console.log('Error in getPFDetails function:', error);
        return [];
    }
}

exports.resolvers = resolvers;
