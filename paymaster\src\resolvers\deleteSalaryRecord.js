// Required dependencies
const commonLib = require('@cksiva09/hrapp-corelib').CommonLib;
const knex = require('knex');
const { ehrTables } = require('../common/tablealias');
const { ApolloError, UserInputError } = require('apollo-server-lambda');
const { getWorkflowProcessInstanceId } = require('../common/commonfunctions');

/**
 * Single comprehensive delete API for all salary forms (206, 207, 360)
 * @param {Object} parent - Parent object
 * @param {Object} args - Arguments passed to the resolver
 * @param {Object} context - Context object containing user info
 * @returns {Object} - Response with operation status
 */
const deleteSalaryRecord = async (parent, args, context) => {
  let organizationDbConnection;

  try {
    console.log('Inside deleteSalaryRecord function');
    const { logInEmpId: loginEmployeeId, userIp } = context;
    const { id, formId } = args;
    organizationDbConnection = knex(context.connection.OrganizationDb);

    // Check employee access rights
    const checkRights = await commonLib.func.checkEmployeeAccessRights(
      organizationDbConnection,
      loginEmployeeId,
      '',
      '',
      'UI',
      false,
      formId
    );

    if (Object.keys(checkRights).length <= 0 || checkRights.Role_Delete === 0) {
      throw '_DB0103';
    }

    // Get table configuration based on form type
    const tableConfig = getTableConfig(formId);

    // Get the record to be deleted
    const recordToDelete = await organizationDbConnection(tableConfig.mainTable)
      .select('*')
      .where(tableConfig.primaryKey, args.formId === 207 ? args.employeeId : id)
      .first();

    if (!recordToDelete) {
      throw 'PST0014'; // Record not found
    }

    // Perform form-specific validation
    if (parseInt(formId) === 206) {
      // FormId 206: Template should not exist in emp_salary_details (207) and salary_revision_details (360)
      const salaryDetailsCount = await organizationDbConnection(ehrTables.employeeSalaryDetails)
        .count('Employee_Id as count')
        .where('Template_Id', recordToDelete.Template_Id)
        .first();

      if (salaryDetailsCount.count > 0) {
        throw 'PST0111';
      }

      const revisionDetailsCount = await organizationDbConnection(ehrTables.salaryRevisionDetails)
        .count('Revision_Id as count')
        .where('Template_Id', recordToDelete.Template_Id)
        .first();

      if (revisionDetailsCount.count > 0) {
        throw 'PST0112';
      }

    } else if (parseInt(formId) === 207) {
      // FormId 207: Check revision records and payslip records
      const employeeId = recordToDelete.Employee_Id;

      const revisionCount = await organizationDbConnection(ehrTables.salaryRevisionDetails)
        .count('Revision_Id as count')
        .where('Employee_Id', employeeId)
        .first();

      if (revisionCount.count > 0) {
        throw 'PST0114';
      }
      if(args.formId === 207){
      const payslipCount = await organizationDbConnection(ehrTables.salaryPayslip)
        .count('Payslip_Id as count')
        .where('Employee_Id', employeeId)
        .first();

      if (payslipCount.count > 0) {
        throw 'PST0115';
      }
      }

    } else if (parseInt(formId) === 360) {
      // FormId 360: Only check if status is Approved or Rejected
      if (recordToDelete.Revision_Status &&
          (recordToDelete.Revision_Status.toLowerCase() === 'approved' ||
           recordToDelete.Revision_Status.toLowerCase() === 'rejected')) {
        throw 'PST0113'
      }
    }

    // Perform deletion in transaction (cascading delete from 3 tables)
    return await organizationDbConnection.transaction(async (trx) => {
      // Delete from allowance table first (foreign key constraint)
      if (tableConfig.allowanceTable) {
        await organizationDbConnection(tableConfig.allowanceTable)
          .where(tableConfig.foreignKey, args.formId === 207 ? args.employeeId : id)
          .delete()
          .transacting(trx);
      }

      // Delete from retirals table second (foreign key constraint)
      if (tableConfig.retiralsTable) {
        await organizationDbConnection(tableConfig.retiralsTable)
          .where(tableConfig.foreignKey, args.formId === 207 ? args.employeeId : id)
          .delete()
          .transacting(trx);
      }

      // Delete from main table last
      await organizationDbConnection(tableConfig.mainTable)
        .where(tableConfig.primaryKey, args.formId === 207 ? args.employeeId : id)
        .delete()
        .transacting(trx);

      // Log system activity
      if(args.formId === 360){
      const responseObject = await getWorkflowProcessInstanceId(
        organizationDbConnection,
        args.id
      );
      if (responseObject && responseObject[0]?.Process_Instance_Id) {
        await commonLib.func.deleteOldApprovalRecords(
          organizationDbConnection,
          responseObject[0].Process_Instance_Id,
          trx
        );
      }
      }
      await commonLib.func.createSystemLogActivities({
        userIp,
        employeeId: loginEmployeeId,
        organizationDbConnection,
        message: `${tableConfig.entityName} deleted successfully`
      });

      return {
        errorCode: "",
        message: `${tableConfig.entityName} deleted successfully`
      };
    });

  } catch (error) {
    console.log('Error in deleteSalaryRecord function', error);
    const errResult = commonLib.func.getError(error, 'PST0116');
    throw new ApolloError(errResult.message, errResult.code);
  } finally {
    if (organizationDbConnection) await organizationDbConnection.destroy();
  }
};



/**
 * Get table configuration based on form ID
 * @param {number} formId - Form ID to determine table configuration
 * @returns {Object} - Configuration object with table details
 */
function getTableConfig(formId) {
  switch (parseInt(formId)) {
    case 206: // Salary Template
      return {
        entityName: 'Salary template',
        mainTable: ehrTables.salaryTemplate,
        allowanceTable: ehrTables.templateAllowanceComponents,
        retiralsTable: ehrTables.templateRetiralComponents,
        primaryKey: 'Template_Id',
        foreignKey: 'Template_Id'
      };
    case 207: // Salary Form
      return {
        entityName: 'Salary details',
        mainTable: ehrTables.employeeSalaryDetails,
        allowanceTable: ehrTables.employeeSalaryAllowance,
        retiralsTable: ehrTables.employeeSalaryRetirals,
        primaryKey: 'Employee_Id',
        foreignKey: 'Employee_Id'
      };
    case 360: // Salary Revision
      return {
        entityName: 'Salary revision',
        mainTable: ehrTables.salaryRevisionDetails,
        allowanceTable: ehrTables.salaryRevisionAllowance,
        retiralsTable: ehrTables.salaryRevisionRetirals,
        primaryKey: 'Revision_Id',
        foreignKey: 'Revision_Id'
      };
    default:
      throw new Error(`Invalid formId: ${formId}`);
  }
}

// Export resolvers
const resolvers = {
  Mutation: {
    deleteSalaryRecord
  }
};

exports.resolvers = resolvers;
