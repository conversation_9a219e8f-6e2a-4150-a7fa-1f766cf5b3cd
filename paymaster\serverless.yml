service: PAYMASTER # service name

plugins:
  - serverless-offline # require plugins
  - serverless-prune-plugin # PlugIn to maintain lambda versioning
  - serverless-domain-manager

provider:
  name: aws
  runtime: nodejs18.x #nodejs run time
  stage: ${opt:stage} # get current stage name
  region: ${opt:region} #region in which to be deployed
  role: ${file(../config.${self:provider.stage}.json):lambdaRole} # Assign role to the lambda functions
  vpc:
    securityGroupIds: ${file(../config.${self:provider.stage}.json):securityGroupIds}
    subnetIds: ${file(../config.${self:provider.stage}.json):subnetIds}
  logs: # enable api gateway logs
    restApi: true

custom:
  customDomain:
    domainName: ${file(../config.${self:provider.stage}.json):customDomainName}
    basePath: "paymaster"
    stage: ${self:provider.stage}
    createRoute53Record: true
    endpointType: "edge"
  prune:
    automatic: true
    number: 3

# Lambda functions
functions:
  graphql:
    handler: src/handler.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: graphql
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(../config.${self:provider.stage}.json):firebaseAuthorizer}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      profileBucket: ${file(../config.${self:provider.stage}.json):profileBucket}
      documentsBucket: ${file(../config.${self:provider.stage}.json):documentsBucket}
      logoBucket: ${file(../config.${self:provider.stage}.json):logoBucket}
      sourceEmailAddress: ${file(../config.${self:provider.stage}.json):sourceEmailAddress}
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}
      customDomainName: ${file(../config.${self:provider.stage}.json):customDomainName}
      stageName: ${self:provider.stage}
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}

  rographql:
    handler: src/rohandler.graphql
    timeout: 900 # Lambda timeout
    events:
      - http:
          path: rographql
          method: post
          cors:
            origin: "*"
            headers:
              - Content-Type
              - X-Amz-Date
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - authorization
              - org_code
              - user_ip
              - refresh_token
              - partnerid
              - additional_headers
            allowCredentials: false
          authorizer:
            arn: ${file(../config.${self:provider.stage}.json):firebaseAuthorizer}
            resultTtlInSeconds: 0
            type: request
    environment: # environment variables
      dbPrefix: ${file(../config.${self:provider.stage}.json):dbPrefix}
      domainName: ${file(../config.${self:provider.stage}.json):domainName}
      customDomainName: ${file(../config.${self:provider.stage}.json):customDomainName}
      stageName: ${self:provider.stage}
      dbSecretName: ${file(../config.${self:provider.stage}.json):dbSecretName}
      region: ${self:provider.region}
      employeeTaxDetailUrl: ${file(../config.${self:provider.stage}.json):employeeTaxDetailUrl} 

resources:
  Resources:
    ApiGatewayRestApi: # Map customized api gateway responses
      Type: AWS::ApiGateway::RestApi
      Properties:
        Name: ${self:service}-${self:provider.stage}

    GatewayResponse4XX: # statusCode 4XX series errorcode
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: DEFAULT_4XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Forbidden." } }'

    GatewayResponse401: # statusCode 401
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: UNAUTHORIZED # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        StatusCode: "401" # API gateway default errorcode
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "Unauthorized request." } }'

    GatewayResponse5XX: # statusCode 5XX series errorcodes
      Type: "AWS::ApiGateway::GatewayResponse"
      Properties:
        ResponseParameters: # Response header to be returned
          gatewayresponse.header.Access-Control-Allow-Origin: "'*'"
          gatewayresponse.header.Access-Control-Allow-Headers: "'*'"
        RestApiId:
          Ref: "ApiGatewayRestApi"
        ResponseType: DEFAULT_5XX # Response Type (Assigned based on the errors Refer AWS API gateway response documentation)
        ResponseTemplates: # Map customized error response
          application/json: '{ "message": {"message": "API gateway timeout." } }'
